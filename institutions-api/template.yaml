AWSTemplateFormatVersion: 2010-09-09
Description: >-
  institutions-api
Transform:
  - AWS::Serverless-2016-10-31

Parameters:
  Stage:
    Type: String
    Default: dev
    AllowedValues:
      - prod
      - dev
      - qa
    Description: Deployment stage (prod/dev/qa)
  ContentBucket:
    Type: String
    Default: doap-content-bucket-211125316254-dev
    Description: S3 bucket name for storing institute logos

# Resources declares the AWS resources that you want to include in the stack
Resources:
  # Database layer definition
  DoapDatabaseLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: !Sub "doap-database-layer-${Stage}"
      Description: Common DB logic
      ContentUri: ../common/layer/database
      CompatibleRuntimes:
        - nodejs20.x
      RetentionPolicy: Retain



  # This is a Lambda function config associated with the source code: get-all-items.js
  getAllItemsFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/get-all-items.getAllItemsHandler
      Runtime: nodejs20.x
      Environment:
        Variables:
          DATABASE_URL: !If 
            - IsProd
            - "postgres://doapadmin:<EMAIL>:5432/doapProdDb"
            - "postgres://doapadmin:<EMAIL>:5432/doapDb"
          CONTENT_BUCKET: !Ref ContentBucket
      Architectures:
        - x86_64
      MemorySize: 128
      Timeout: 100
      Description: get all institutes from array.
      Layers:
        - !Ref DoapDatabaseLayer
      Events:
        Api:
          Type: Api
          Properties:
            Path: /institutes
            Method: GET
            RestApiId: !Ref ApiGateway

  # This is a Lambda function config associated with the source code: get-by-id.js
  getByIdFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/get-by-id.getByIdHandler
      Runtime: nodejs20.x
      Environment:
        Variables:
          DATABASE_URL: !If 
            - IsProd
            - "postgres://doapadmin:<EMAIL>:5432/doapProdDb"
            - "postgres://doapadmin:<EMAIL>:5432/doapDb"
          CONTENT_BUCKET: !Ref ContentBucket
      Architectures:
        - x86_64
      MemorySize: 128
      Timeout: 100
      Description: A simple example includes a HTTP get method to get one item by id from database
      Layers:
        - !Ref DoapDatabaseLayer
      Events:
        Api:
          Type: Api
          Properties:
            Path: /institutes/{id}
            Method: GET
            RestApiId: !Ref ApiGateway

  # This is a Lambda function config associated with the source code: put-item.js
  putItemFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/put-item.putItemHandler
      Runtime: nodejs20.x
      Environment:
        Variables:
          DATABASE_URL: !If 
            - IsProd
            - "postgres://doapadmin:<EMAIL>:5432/doapProdDb"
            - "postgres://doapadmin:<EMAIL>:5432/doapDb"
          CONTENT_BUCKET: !Ref ContentBucket
      Architectures:
        - x86_64
      MemorySize: 128
      Timeout: 100
      Description: A simple example includes a HTTP post method to add one item to database.
      Layers:
        - !Ref DoapDatabaseLayer
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - s3:PutObject
              Resource:
                - !Sub "arn:aws:s3:::${ContentBucket}/campuses/*"
                - !Sub "arn:aws:s3:::${ContentBucket}/campuses"
      Events:
        Api:
          Type: Api
          Properties:
            Path: /institutes/{id}
            Method: PUT
            RestApiId: !Ref ApiGateway

  # This is a Lambda function config associated with the source code: post-item.js
  postItemFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/post-item.postItemHandler
      Runtime: nodejs20.x
      Environment:
        Variables:
          DATABASE_URL: !If 
            - IsProd
            - "postgres://doapadmin:<EMAIL>:5432/doapProdDb"
            - "postgres://doapadmin:<EMAIL>:5432/doapDb"
          CONTENT_BUCKET: !Ref ContentBucket
      Architectures:
        - x86_64
      MemorySize: 128
      Timeout: 100
      Description: A simple example includes a HTTP post method to add one item to database.
      Layers:
        - !Ref DoapDatabaseLayer
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - s3:PutObject
              Resource:
                - !Sub "arn:aws:s3:::${ContentBucket}/campuses/*"
                - !Sub "arn:aws:s3:::${ContentBucket}/campuses"
      Events:
        Api:
          Type: Api
          Properties:
            Path: /institutes
            Method: POST
            RestApiId: !Ref ApiGateway

  optionsFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/options-handler.optionsHandler
      Runtime: nodejs20.x
      Architectures:
        - x86_64
      MemorySize: 128
      Timeout: 100
      Description: Handles OPTIONS requests for CORS preflight
      Layers:
        - !Ref DoapDatabaseLayer
      Events:
        ApiInstitutes:
          Type: Api
          Properties:
            Path: /institutes
            Method: OPTIONS
            RestApiId: !Ref ApiGateway
        ApiInstituteById:
          Type: Api
          Properties:
            Path: /institutes/{id}
            Method: OPTIONS
            RestApiId: !Ref ApiGateway

  ApplicationResourceGroup:
    Type: AWS::ResourceGroups::Group
    Properties:
      Name:
        Fn::Sub: ApplicationInsights-SAM-${AWS::StackName}
      ResourceQuery:
        Type: CLOUDFORMATION_STACK_1_0

  ApplicationInsightsMonitoring:
    Type: AWS::ApplicationInsights::Application
    Properties:
      ResourceGroupName:
        Ref: ApplicationResourceGroup
      AutoConfigurationEnabled: true

  ApiGateway:
    Type: AWS::Serverless::Api
    Properties:
      StageName: !Ref Stage
      EndpointConfiguration: 
        Type: EDGE
      TracingEnabled: true
      # Remove the MethodSettings section entirely to avoid logging configuration

Conditions:
  IsProd: !Equals [!Ref Stage, "prod"]
  IsDev: !Equals [!Ref Stage, "dev"]
  IsQa: !Equals [!Ref Stage, "qa"]

Outputs:
  ApiGatewayRestApiId:
    Description: The ID of the API Gateway created by SAM
    Value: !Ref ApiGateway
    Export:
      Name: !Sub "${AWS::StackName}-ApiGatewayRestApiId-${Stage}"
  WebEndpoint:
    Description: API Gateway endpoint URL for the specified stage
    Value: !Sub "https://${ApiGateway}.execute-api.${AWS::Region}.amazonaws.com/${Stage}/"

Globals:
  Function:
    Tracing: Active
    LoggingConfig:
      LogFormat: JSON
  Api:
    TracingEnabled: true
