// upload-institute-logos.js
// Script to upload all institute logos from the database to S3 as JPEG images

const { S3Client, PutObjectCommand } = require('@aws-sdk/client-s3');
const { Client } = require('pg');

// CONFIGURE THESE:
const DATABASE_URL = "postgres://doapadmin:<EMAIL>:5432/doapDb"; // <-- Set your DB connection string
const S3_BUCKET = 'doap-content-bucket-211125316254-dev'; // <-- Set your S3 bucket name
const REGION = 'us-east-1'; // <-- Set your S3 region

// Initialize clients
const db = new Client({
  connectionString: DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});
const s3 = new S3Client({ region: REGION });

async function uploadInstituteLogos() {
  await db.connect();

  // Get all institutes with logos
  const res = await db.query('SELECT id, logo FROM institutes WHERE logo IS NOT NULL');

  for (const row of res.rows) {
    const { id, logo } = row;
    if (!logo) continue;

    const key = `campuses/${id}/image.jpg`;

    try {
      await s3.send(new PutObjectCommand({
        Bucket: S3_BUCKET,
        Key: key,
        Body: logo,
        ContentType: 'image/jpeg'
      }));
      console.log(`Uploaded logo for institute ${id} to ${key}`);
    } catch (err) {
      console.error(`Failed to upload logo for institute ${id}:`, err);
    }
  }

  await db.end();
}

uploadInstituteLogos().catch(console.error); 