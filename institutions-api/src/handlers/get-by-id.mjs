import {
  STATUSCODE
} from "../constants/status-codes.mjs";
import { instituteOperations } from "../database/index.mjs";
import { createResponse } from "../utils/response-utils.mjs";
import { getInstituteLogoUrl } from '../utils/s3-utils.mjs';


/**
 * Handler for GET /institutes/{id} endpoint
 * Retrieves a specific institute by ID from the database
 */
export const getByIdHandler = async (event) => {
  try {

    // Get id from path parameters
    const id = event.pathParameters?.id;

    // Check if ID is provided
    if (!id) {
      return createResponse(STATUSCODE.BAD_REQUEST, { message: "Missing institute ID" });
    }

    // Query database for the specific institute
    const institute = await instituteOperations.getInstituteById(parseInt(id, 10));

    // Check if institute was found
    if (!institute) {
      return createResponse(STATUSCODE.NOT_FOUND, { message: `Institute with ID ${id} not found` });
    }

    // Add logo URL
    const region = process.env.AWS_REGION || 'us-east-1';
    const bucket = process.env.CONTENT_BUCKET;
    const instituteWithLogoUrl = {
      ...institute,
      logo: getInstituteLogoUrl({ id: institute.id, bucket, region })
    };

    // Create successful response
    const response = createResponse(STATUSCODE.SUCCESS, [instituteWithLogoUrl]);

    // Log response details
    console.info(
      `Response from: ${event.path} statusCode: ${response.statusCode} institute: ${institute?.name}`
    );

    return response;
  } catch (err) {
    // Log the error
    console.error(`Error retrieving institute with ID ${event.pathParameters?.id}:`, err);

    // Return appropriate error response
    const errorResponse = createResponse(
      err.statusCode || STATUSCODE.SERVER_ERROR,
      { message: err.message || "Error retrieving institute", error: err.toString() }
    );

    console.info(
      `Error response from: ${event.path} statusCode: ${errorResponse.statusCode} body: ${errorResponse.body}`
    );

    return errorResponse;
  }
};