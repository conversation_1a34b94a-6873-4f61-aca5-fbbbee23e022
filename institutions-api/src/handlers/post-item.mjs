import {
  STATUSCODE
} from "../constants/status-codes.mjs";
import { instituteOperations } from "../database/index.mjs";
import { createResponse } from "../utils/response-utils.mjs";
import { uploadInstituteLogo } from '../utils/s3-utils.mjs';

const CONTENT_BUCKET = process.env.CONTENT_BUCKET;

/**
 * Handler for POST /institutes endpoint
 * Creates a new institute in the database
 */
export const postItemHandler = async (event) => {
  try {
    // Parse request body
    const body = JSON.parse(event.body);
    const { name, logo, email, contact_number } = body;

    // Validate required fields
    if (!name) {
      return createResponse(STATUSCODE.BAD_REQUEST, { message: "Missing name in request body." });
    }

    // Step 1: Create institute without logo
    const result = await instituteOperations.createInstitute({
      name,
      email,
      logo: undefined,
      contact_number
    });

    let updatedResult = result;
    let logoUrl = undefined;
    // Step 2: If logo is provided, upload to S3 and update the record
    if (logo && CONTENT_BUCKET && result && result.id) {
      const region = process.env.AWS_REGION || 'us-east-1';
      const { key: logoKey, url: logoUrlResult } = await uploadInstituteLogo({
        id: result.id,
        logoBase64: logo,
        bucket: CONTENT_BUCKET,
        region
      });
      // Update the institute with the logo key
      updatedResult = await instituteOperations.updateInstitute({
        id: result.id,
        logo: logoKey
      });
      logoUrl = logoUrlResult;
    }

    // Create successful response
    const response = createResponse(
      STATUSCODE.SUCCESS,
      {
        message: "Institute created successfully.",
        institute: {
          ...updatedResult,
          logo: logoUrl || updatedResult.logo
        }
      }
    );

    // Log response details
    console.info(
      `Response from: ${event.path} statusCode: ${response.statusCode} institute: ${name}`
    );

    return response;
  } catch (err) {
    // Log the error
    console.error("Error creating institute:", err);

    // Return appropriate error response
    return createResponse(
      err.statusCode || STATUSCODE.SERVER_ERROR,
      { message: "Error creating institute", error: err.toString() }
    );
  }
}; 