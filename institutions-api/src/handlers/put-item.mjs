import {
  STATUSCODE
} from "../constants/status-codes.mjs";
import { instituteOperations } from "../database/index.mjs";
import { createResponse } from "../utils/response-utils.mjs";
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { uploadInstituteLogo } from '../utils/s3-utils.mjs';

const s3 = new S3Client({ region: process.env.AWS_REGION || 'us-east-1' });
const CONTENT_BUCKET = process.env.CONTENT_BUCKET;

/**
 * Handler for PUT /institutes/{id} endpoint
 * Updates an existing institute in the database
 */
export const putItemHandler = async (event) => {
  try {
    // Get id from path parameters
    const id = event.pathParameters?.id;
    if (!id) {
      return createResponse(STATUSCODE.BAD_REQUEST, { message: "Missing id in path parameters." });
    }

    // Parse request body
    const body = JSON.parse(event.body);
    const { name, logo, email, contact_number } = body;

    // Check if institute exists
    const existingInstitute = await instituteOperations.getInstituteById(parseInt(id, 10));
    if (!existingInstitute) {
      return createResponse(STATUSCODE.NOT_FOUND, { message: `Institute with ID ${id} not found.` });
    }

    let logoKey = undefined;
    let logoUrl = undefined;
    if (logo && CONTENT_BUCKET) {
      const region = process.env.AWS_REGION || 'us-east-1';
      const { key: logoKeyResult, url: logoUrlResult } = await uploadInstituteLogo({
        id,
        logoBase64: logo,
        bucket: CONTENT_BUCKET,
        region
      });
      logoKey = logoKeyResult;
      logoUrl = logoUrlResult;
    }

    // Update institute
    const result = await instituteOperations.updateInstitute({
      id: parseInt(id, 10),
      name,
      email,
      logo: logoKey !== undefined ? logoKey : undefined,
      contact_number
    });

    // Create successful response
    const response = createResponse(
      STATUSCODE.SUCCESS,
      {
        message: "Institute updated successfully.",
        institute: {
          ...result,
          logo: logoUrl || result.logo
        }
      }
    );

    // Log response details
    console.info(
      `Response from: ${event.path} statusCode: ${response.statusCode} institute: ${result.name}`
    );

    return response;
  } catch (err) {
    // Log the error
    console.error("Error updating institute:", err);

    // Return appropriate error response
    return createResponse(
      err.statusCode || STATUSCODE.SERVER_ERROR,
      { message: "Error updating institute", error: err.toString() }
    );
  }
};