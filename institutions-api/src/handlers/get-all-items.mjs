import {
  STATUSCODE
} from "../constants/status-codes.mjs";
import { instituteOperations } from "../database/index.mjs";
import { createResponse } from "../utils/response-utils.mjs";
import { getInstituteLogoUrl } from '../utils/s3-utils.mjs';

/**
 * Parse pagination parameters from query string
 * @param {Object} queryStringParameters - Query string parameters
 * @returns {Object} Pagination parameters
 */
const parsePaginationParams = (queryStringParameters) => {
  const params = queryStringParameters || {};

  // Default page is 1, default page size is 10
  const page = parseInt(params.page, 10) || 1;
  const pageSize = parseInt(params.pageSize, 10) || 10;

  // Ensure page and pageSize are positive integers
  return {
    page: Math.max(1, page),
    pageSize: Math.min(100, Math.max(1, pageSize)) // Limit pageSize to 100
  };
};

/**
 * Handler for GET /institutes endpoint
 * Retrieves institutes from the database with pagination
 */
export const getAllItemsHandler = async (event) => {
  try {
    // Parse pagination parameters
    const { page, pageSize } = parsePaginationParams(event.queryStringParameters);

    // Parse filter parameters
    const filters = {};
    if (event.queryStringParameters) {
      if (event.queryStringParameters.name) filters.name = event.queryStringParameters.name;
      if (event.queryStringParameters.email) filters.email = event.queryStringParameters.email;
      if (event.queryStringParameters.contact_number) filters.contact_number = event.queryStringParameters.contact_number;
    }

    // Parse fields (columns to select)
    let fields = null;
    if (event.queryStringParameters && event.queryStringParameters.fields) {
      fields = event.queryStringParameters.fields.split(',').map(s => s.trim()).filter(Boolean);
    }

    // Calculate offset
    const offset = (page - 1) * pageSize;

    // Query database for institutes with pagination, filters, and fields
    const [institutes, totalCount] = await Promise.all([
      instituteOperations.getPaginatedInstitutes(offset, pageSize, filters, fields),
      instituteOperations.getInstitutesTotalCount(filters)
    ]);

    // Calculate total pages
    const totalPages = Math.ceil(totalCount / pageSize);

    // Create pagination metadata
    const pagination = {
      page,
      pageSize,
      totalCount,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1
    };

    // Add logo URL to each institute
    const region = process.env.AWS_REGION || 'us-east-1';
    const bucket = process.env.CONTENT_BUCKET;
    const institutesWithLogoUrl = institutes.map(inst => ({
      ...inst,
      logo: getInstituteLogoUrl({ id: inst.id, bucket, region })
    }));

    // Create successful response with institutes and pagination metadata
    const response = createResponse(STATUSCODE.SUCCESS, {
      institutes: institutesWithLogoUrl,
      pagination
    });

    // Log response details
    console.info(
      `Response from: ${event.path} statusCode: ${response.statusCode} page: ${page} pageSize: ${pageSize} totalCount: ${totalCount}`
    );

    return response;
  } catch (err) {
    // Log the error
    console.error("Error retrieving institutes:", err);

    // Return appropriate error response
    const errorResponse = createResponse(
      err.statusCode || STATUSCODE.SERVER_ERROR,
      { message: err.message || "Error retrieving institutes", error: err.toString() }
    );

    console.info(
      `Error response from: ${event.path} statusCode: ${errorResponse.statusCode} body: ${errorResponse.body}`
    );

    return errorResponse;
  }
};