import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';

/**
 * Uploads a base64-encoded logo to S3 for an institute and returns the S3 key and full URL.
 * @param {Object} params
 * @param {number|string} params.id - Institute ID
 * @param {string} params.logoBase64 - Base64-encoded image string
 * @param {string} params.bucket - S3 bucket name
 * @param {string} params.region - AWS region
 * @returns {Promise<{ key: string, url: string }>} S3 key and full URL
 */
export async function uploadInstituteLogo({ id, logoBase64, bucket, region }) {
  if (!id || !logoBase64 || !bucket) throw new Error('Missing required parameters for S3 upload');
  const s3 = new S3Client({ region: region || 'us-east-1' });
  const key = `campuses/${id}/image.jpg`;
  const buffer = Buffer.from(logoBase64, 'base64');
  await s3.send(new PutObjectCommand({
    Bucket: bucket,
    Key: key,
    Body: buffer,
    ContentType: 'image/jpeg'
  }));
  const url = `https://${bucket}.s3.${region || 'us-east-1'}.amazonaws.com/${key}`;
  return { key, url };
}

/**
 * Returns the S3 URL for the logo of the given institute id.
 * @param {Object} params
 * @param {number|string} params.id - Institute ID
 * @param {string} params.bucket - S3 bucket name
 * @param {string} params.region - AWS region
 * @returns {string} S3 URL for the logo
 */
export function getInstituteLogoUrl({ id, bucket, region }) {
  if (!id || !bucket) throw new Error('Missing required parameters for logo URL');
  const key = `campuses/${id}/image.jpg`;
  return `https://${bucket}.s3.${region || 'us-east-1'}.amazonaws.com/${key}`;
} 