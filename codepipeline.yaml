AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: >
  This template deploys a CodePipeline with its required resources.

  The following stages are predefined in this template:
  - Source
  - UpdatePipeline
  # - UnitTests
  - BuildAndPackage
  - DeployTest
  # - IntegrationTests
  - DeployProd

  **WARNING** You will be billed for the AWS resources used if you create a stack from this template.

# To deploy this template and connect to the main git branch, run this against the leading account:
# `sam deploy --config-file codepipeline/samconfig-pipeline.toml --config-env pipeline`.

Metadata:
  cfn-lint:
    config:
      ignore_checks:
        - I3011
        - I3013

Parameters:
  DeploymentStage:
    Type: String
    Default: dev
  ProjectSubfolders:
    Type: String
    Default: "institutions-api,collections-api,send-email-api"
  CodeCommitRepositoryName:
    Type: String
    Default: "doap_mono_serverless"
  MainGitBranch:
    Type: String
    Default: "development"
  MonorepoSsmPrefix:
    Type: String
    Default: "serverless-dev-monorepo"
  CodeBuildImage:
    Type: String
    Default: aws/codebuild/amazonlinux2-x86_64-standard:5.0
  # Stage 1 Parameters
  TestConfigEnvName:
    Type: String
    Default: "1"
  TestingPipelineExecutionRole:
    Type: String
    Default: "arn:aws:iam::************:role/aws-sam-cli-managed-serverles-PipelineExecutionRole-GjlNtx6dAqaU"
  TestingCloudFormationExecutionRole:
    Type: String
    Default: "arn:aws:iam::************:role/aws-sam-cli-managed-serve-CloudFormationExecutionRo-wo85DcUZJU5s"
  # Stage 2 Parameters
  ProdConfigEnvName:
    Type: String
    Default: "2"
  ProdPipelineExecutionRole:
    Type: String
    Default: "arn:aws:iam::************:role/aws-sam-cli-managed-serverles-PipelineExecutionRole-yHNOnwrYXLYY"
  ProdCloudFormationExecutionExeRole:
    Type: String
    Default: "arn:aws:iam::************:role/aws-sam-cli-managed-serve-CloudFormationExecutionRo-N2AhQOSmG4HI"

Resources:
  #  ____  _            _ _
  # |  _ \(_)_ __   ___| (_)_ __   ___
  # | |_) | | '_ \ / _ | | | '_ \ / _ \
  # |  __/| | |_) |  __| | | | | |  __/
  # |_|   |_| .__/ \___|_|_|_| |_|\___|
  #         |_|
  Pipeline:
    Type: AWS::CodePipeline::Pipeline
    Properties:
      ArtifactStore:
        Location: !Ref PipelineArtifactsBucket
        Type: S3
      RoleArn: !Sub "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/serverless-pipeline-${DeploymentStage}"
      RestartExecutionOnUpdate: false
      Stages:
        - Name: Source
          Actions:
            - Name: SourceCodeRepo
              ActionTypeId:
                Version: "1"
                Category: Source
                Owner: AWS
                Provider: CodeCommit
              Configuration:
                BranchName: !Ref MainGitBranch
                RepositoryName: !Ref CodeCommitRepositoryName
                PollForSourceChanges: false
              OutputArtifacts:
                - Name: SourceCodeAsZip
              RunOrder: 1

        - Name: DeployLambda
          Actions:
            - Name: DeployLambda
              ActionTypeId:
                Category: Build
                Owner: AWS
                Provider: CodeBuild
                Version: "1"
              Configuration:
                ProjectName: !Sub "doap-mono-serverless-lambda-codebuild-${DeploymentStage}"
                EnvironmentVariables: !Sub '[{"name":"ACCOUNT_ID","value":"${AWS::AccountId}","type":"PLAINTEXT"},{"name":"DEPLOYMENT_STAGE","value":"${DeploymentStage}","type":"PLAINTEXT"}]'
              InputArtifacts:
                - Name: SourceCodeAsZip
              RunOrder: 1

        - Name: UpdatePipeline
          Actions:
            - Name: CreateChangeSet
              ActionTypeId:
                Category: Deploy
                Owner: AWS
                Provider: CloudFormation
                Version: "1"
              Configuration:
                ActionMode: CHANGE_SET_REPLACE
                RoleArn: !GetAtt PipelineStackCloudFormationExecutionRole.Arn
                StackName: !Ref AWS::StackName
                ChangeSetName: !Sub "${AWS::StackName}-ChangeSet"
                TemplatePath: !Sub "SourceCodeAsZip::codepipeline.yaml"
                Capabilities: CAPABILITY_NAMED_IAM
              InputArtifacts:
                - Name: SourceCodeAsZip
              RunOrder: 1
            - Name: ExecuteChangeSet
              ActionTypeId:
                Category: Deploy
                Owner: AWS
                Provider: CloudFormation
                Version: "1"
              Configuration:
                ActionMode: CHANGE_SET_EXECUTE
                RoleArn: !GetAtt PipelineStackCloudFormationExecutionRole.Arn
                StackName: !Ref AWS::StackName
                ChangeSetName: !Sub ${AWS::StackName}-ChangeSet
              OutputArtifacts:
                - Name: !Sub ${AWS::StackName}ChangeSet
              RunOrder: 2

        # - Name: UnitTest
        #   Actions:
        #     - Name: UnitTest
        #       ActionTypeId:
        #         Category: Build
        #         Owner: AWS
        #         Provider: CodeBuild
        #         Version: "1"
        #       Configuration:
        #         ProjectName: !Ref CodeBuildProjectUnitTest
        #       InputArtifacts:
        #         - Name: SourceCodeAsZip

        - Name: BuildAndPackage
          Actions:
            - Name: BuildInstitutionsApi
              ActionTypeId:
                Category: Build
                Owner: AWS
                Provider: CodeBuild
                Version: "1"
              Configuration:
                ProjectName: !Sub "doap-mono-serverless-build-package-${DeploymentStage}"
                EnvironmentVariables: !Sub '[{"name":"ENV_CONFIG_NAME","value":"${TestConfigEnvName}","type":"PLAINTEXT"},{"name":"ENV_PIPELINE_EXECUTION_ROLE","value":"${TestingPipelineExecutionRole}","type":"PLAINTEXT"},{"name":"ENV_CLOUDFORMATION_EXECUTION_ROLE","value":"${TestingCloudFormationExecutionRole}","type":"PLAINTEXT"},{"name":"PROJECT_SUBFOLDERS","value":"institutions-api","type":"PLAINTEXT"},{"name":"API_STAGE","value":"institutions","type":"PLAINTEXT"},{"name":"BUILD_STAGE","value":"${DeploymentStage}","type":"PLAINTEXT"}]'
              InputArtifacts:
                - Name: SourceCodeAsZip
              OutputArtifacts:
                - Name: InstitutionsApiArtifact
              RunOrder: 1

            - Name: BuildCollectionsApi
              ActionTypeId:
                Category: Build
                Owner: AWS
                Provider: CodeBuild
                Version: "1"
              Configuration:
                ProjectName: !Sub "doap-mono-serverless-build-package-${DeploymentStage}"
                EnvironmentVariables: !Sub '[{"name":"ENV_CONFIG_NAME","value":"${TestConfigEnvName}","type":"PLAINTEXT"},{"name":"ENV_PIPELINE_EXECUTION_ROLE","value":"${TestingPipelineExecutionRole}","type":"PLAINTEXT"},{"name":"ENV_CLOUDFORMATION_EXECUTION_ROLE","value":"${TestingCloudFormationExecutionRole}","type":"PLAINTEXT"},{"name":"PROJECT_SUBFOLDERS","value":"collections-api","type":"PLAINTEXT"},{"name":"API_STAGE","value":"collections","type":"PLAINTEXT"},{"name":"BUILD_STAGE","value":"${DeploymentStage}","type":"PLAINTEXT"}]'
              InputArtifacts:
                - Name: SourceCodeAsZip
              OutputArtifacts:
                - Name: CollectionsApiArtifact
              RunOrder: 2

            - Name: BuildSendEmailApi
              ActionTypeId:
                Category: Build
                Owner: AWS
                Provider: CodeBuild
                Version: "1"
              Configuration:
                ProjectName: !Sub "doap-mono-serverless-build-package-${DeploymentStage}"
                EnvironmentVariables: !Sub '[{"name":"ENV_CONFIG_NAME","value":"${TestConfigEnvName}","type":"PLAINTEXT"},{"name":"ENV_PIPELINE_EXECUTION_ROLE","value":"${TestingPipelineExecutionRole}","type":"PLAINTEXT"},{"name":"ENV_CLOUDFORMATION_EXECUTION_ROLE","value":"${TestingCloudFormationExecutionRole}","type":"PLAINTEXT"},{"name":"PROJECT_SUBFOLDERS","value":"send-email-api","type":"PLAINTEXT"},{"name":"API_STAGE","value":"send-email-api","type":"PLAINTEXT"},{"name":"BUILD_STAGE","value":"${DeploymentStage}","type":"PLAINTEXT"}]'
              InputArtifacts:
                - Name: SourceCodeAsZip
              OutputArtifacts:
                - Name: SendEmailApiArtifact
              RunOrder: 3

        # - Name: DeployTest
        #   Actions:
        #     - Name: DeployTest
        #       ActionTypeId:
        #         Category: Build
        #         Owner: AWS
        #         Provider: CodeBuild
        #         Version: "1"
        #       Configuration:
        #         ProjectName: !Sub "doap-mono-serverless-codebuild-${DeploymentStage}"
        #         EnvironmentVariables: !Sub '[{"name":"ENV_CONFIG_NAME","value":"${TestConfigEnvName}","type":"PLAINTEXT"},{"name":"ENV_PIPELINE_EXECUTION_ROLE","value":"${TestingPipelineExecutionRole}","type":"PLAINTEXT"},{"name":"ENV_CLOUDFORMATION_EXECUTION_ROLE","value":"${TestingCloudFormationExecutionRole}","type":"PLAINTEXT"}]'
        #       InputArtifacts:
        #         - Name: BuildArtifactAsZip
        #       RunOrder: 1
        # - Name: IntegrationTest
        #   ActionTypeId:
        #     Category: Build
        #     Owner: AWS
        #     Provider: CodeBuild
        #     Version: "1"
        #   Configuration:
        #     ProjectName: !Ref CodeBuildProjectIntegrationTest
        #     EnvironmentVariables: !Sub |
        #       [
        #         {"name": "ENV_PIPELINE_EXECUTION_ROLE", "value": "${TestingPipelineExecutionRole}"}
        #       ]
        #   InputArtifacts:
        #     - Name: SourceCodeAsZip
        #   RunOrder: 2

        # - Name: DeployProd
        #   Actions:
        #     # uncomment this to have a manual approval step before deployment to production
        #     # - Name: ManualApproval
        #     #   ActionTypeId:
        #     #    Category: Approval
        #     #    Owner: AWS
        #     #    Provider: Manual
        #     #    Version: "1"
        #     #   RunOrder: 1
        #     - Name: DeployProd
        #       ActionTypeId:
        #         Category: Build
        #         Owner: AWS
        #         Provider: CodeBuild
        #         Version: "1"
        #       RunOrder: 2 # keeping run order as 2 in case manual approval is enabled
        #       Configuration:
        #         ProjectName: !Sub "doap-mono-serverless-codebuild-${DeploymentStage}"
        #         EnvironmentVariables: !Sub '[{"name":"ENV_CONFIG_NAME","value":"${ProdConfigEnvName}","type":"PLAINTEXT"},{"name":"ENV_PIPELINE_EXECUTION_ROLE","value":"${ProdPipelineExecutionRole}","type":"PLAINTEXT"},{"name":"ENV_CLOUDFORMATION_EXECUTION_ROLE","value":"${ProdCloudFormationExecutionExeRole}","type":"PLAINTEXT"}]'
        #       InputArtifacts:
        #         - Name: BuildArtifactAsZip

      DisableInboundStageTransitions:
        - Reason: "Production deployments are initially disabled until we get some core level development completed."
          StageName: "DeployProd"

  PipelineArtifactsBucket:
    Type: AWS::S3::Bucket
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      BucketName: !Sub "doap-mono-serverless-pipeline-artifacts-${AWS::AccountId}-${DeploymentStage}"
      VersioningConfiguration:
        Status: Enabled
      LoggingConfiguration:
        DestinationBucketName: !Ref PipelineArtifactsLoggingBucket
        LogFilePrefix: "artifacts-logs"
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256

  PipelineArtifactsBucketPolicy:
    Type: AWS::S3::BucketPolicy
    Properties:
      Bucket: !Ref PipelineArtifactsBucket
      PolicyDocument:
        Statement:
          - Effect: Deny
            Action: s3:*
            Principal: "*"
            Resource:
              - !Sub "${PipelineArtifactsBucket.Arn}/*"
              - !GetAtt PipelineArtifactsBucket.Arn
            Condition:
              Bool:
                aws:SecureTransport: false
          - Effect: Allow
            Action: s3:*
            Principal:
              AWS:
                - !Sub "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/serverless-pipeline-${DeploymentStage}"
            Resource:
              - !Sub "${PipelineArtifactsBucket.Arn}/*"
              - !GetAtt PipelineArtifactsBucket.Arn

  PipelineArtifactsLoggingBucket:
    Type: AWS::S3::Bucket
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      AccessControl: "LogDeliveryWrite"
      OwnershipControls:
        Rules:
          - ObjectOwnership: ObjectWriter
      VersioningConfiguration:
        Status: Enabled
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256

  PipelineArtifactsLoggingBucketPolicy:
    Type: AWS::S3::BucketPolicy
    Properties:
      Bucket: !Ref PipelineArtifactsLoggingBucket
      PolicyDocument:
        Statement:
          - Effect: Deny
            Action: s3:*
            Principal: "*"
            Resource:
              - !Sub "${PipelineArtifactsLoggingBucket.Arn}/*"
              - !GetAtt PipelineArtifactsLoggingBucket.Arn
            Condition:
              Bool:
                aws:SecureTransport: false

  # PipelineStackCloudFormationExecutionRole is used for the pipeline to self mutate
  PipelineStackCloudFormationExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          Effect: Allow
          Action: sts:AssumeRole
          Principal:
            Service: cloudformation.amazonaws.com
      Policies:
        - PolicyName: GrantCloudFormationFullAccess
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action: "*"
                Resource: "*"

  #   ____          _      ____        _ _     _
  #  / ___|___   __| | ___| __ ) _   _(_| | __| |
  # | |   / _ \ / _` |/ _ |  _ \| | | | | |/ _` |
  # | |__| (_) | (_| |  __| |_) | |_| | | | (_| |
  #  \____\___/ \__,_|\___|____/ \__,_|_|_|\__,_|
  CodeBuildServiceRole:
    Type: AWS::IAM::Role
    Properties:
      Tags:
        - Key: Role
          Value: aws-sam-pipeline-codebuild-service-role
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Action: sts:AssumeRole
            Principal:
              Service:
                - codebuild.amazonaws.com
      Policies:
        - PolicyName: CodeBuildLogs
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource:
                  - !Sub "arn:${AWS::Partition}:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/codebuild/*"
        - PolicyName: CodeBuildArtifactsBucket
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:GetObjectVersion
                  - s3:PutObject
                Resource:
                  - !Sub "arn:${AWS::Partition}:s3:::${PipelineArtifactsBucket}/*"
        - PolicyName: AssumeStagePipExecutionRoles
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action: sts:AssumeRole
                Resource: "*"
                Condition:
                  StringEquals:
                    aws:ResourceTag/Role: pipeline-execution-role

  # CodeBuildProjectUnitTest:
  #   Type: AWS::CodeBuild::Project
  #   Properties:
  #     Artifacts:
  #       Type: CODEPIPELINE
  #     Environment:
  #       Type: LINUX_CONTAINER
  #       ComputeType: BUILD_GENERAL1_SMALL
  #       Image: !Ref CodeBuildImage
  #       EnvironmentVariables:
  #         - Name: PROJECT_SUBFOLDER
  #           Value: !Ref ProjectSubfolder
  #     ServiceRole: !GetAtt CodeBuildServiceRole.Arn
  #     Source:
  #       Type: CODEPIPELINE
  #       BuildSpec: !Sub "${ProjectSubfolder}/codepipeline/buildspec_unit_test.yml"

  CodeBuildProjectBuildAndPackage:
    Type: AWS::CodeBuild::Project
    Properties:
      Name: !Sub "doap-mono-serverless-build-package-${DeploymentStage}"
      Artifacts:
        Type: CODEPIPELINE
      Environment:
        Type: LINUX_CONTAINER
        ComputeType: BUILD_GENERAL1_SMALL
        Image: !Ref CodeBuildImage
        PrivilegedMode: true
        EnvironmentVariables:
          - Name: PROJECT_SUBFOLDERS
            Value: !Ref ProjectSubfolders
          - Name: TESTING_PIPELINE_EXECUTION_ROLE
            Value: !Ref TestingPipelineExecutionRole
          - Name: TESTING_ENV_CONFIG_NAME
            Value: !Ref TestConfigEnvName
          - Name: PROD_PIPELINE_EXECUTION_ROLE
            Value: !Ref ProdPipelineExecutionRole
          - Name: PROD_ENV_CONFIG_NAME
            Value: !Ref ProdConfigEnvName
      ServiceRole: !GetAtt CodeBuildServiceRole.Arn
      Source:
        Type: CODEPIPELINE
        BuildSpec: !Sub "codepipeline/buildspec_build_package.yml"

  # CodeBuildProjectIntegrationTest:
  #   Type: AWS::CodeBuild::Project
  #   Properties:
  #     Artifacts:
  #       Type: CODEPIPELINE
  #     Environment:
  #       Type: LINUX_CONTAINER
  #       ComputeType: BUILD_GENERAL1_SMALL
  #       Image: !Ref CodeBuildImage
  #       EnvironmentVariables:
  #         - Name: PROJECT_SUBFOLDER
  #           Value: !Ref ProjectSubfolder
  #     ServiceRole: !GetAtt CodeBuildServiceRole.Arn
  #     Source:
  #       Type: CODEPIPELINE
  #       BuildSpec: !Sub "${ProjectSubfolder}/codepipeline/buildspec_integration_test.yml"

  CodeBuildProjectDeploy:
    Type: AWS::CodeBuild::Project
    Properties:
      Name: !Sub "doap-mono-serverless-codebuild-${DeploymentStage}"
      Artifacts:
        Type: CODEPIPELINE
      Environment:
        Type: LINUX_CONTAINER
        ComputeType: BUILD_GENERAL1_SMALL
        Image: !Ref CodeBuildImage
        EnvironmentVariables:
          - Name: PROJECT_SUBFOLDERS
            Value: !Ref ProjectSubfolders
      ServiceRole: !GetAtt CodeBuildServiceRole.Arn
      Source:
        Type: CODEPIPELINE
        BuildSpec: !Sub "codepipeline/buildspec_deploy.yml"

  #    _____                      __________
  #   /     \   ____   ____   ____\______   \ ____ ______   ____
  #  /  \ /  \ /  _ \ /    \ /  _ \|       _// __ \\____ \ /  _ \
  # /    Y    (  <_> )   |  (  <_> )    |   \  ___/|  |_> >  <_> )
  # \____|__  /\____/|___|  /\____/|____|_  /\___  >   __/ \____/
  #         \/            \/              \/     \/|__|
  MonorepoTriggerFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "serverless-pipeline-lambda-${AWS::AccountId}-${DeploymentStage}"
      PackageType: Zip
      CodeUri:
        Bucket: !Sub "doap-mono-serverless-pipeline-lambda-${AWS::AccountId}-${DeploymentStage}"
        Key: codepipeline-lambda.zip
      # CodeUri: codepipeline-lambda/
      Handler: lambda.lambda_handler
      Runtime: python3.9
      Timeout: 30
      Layers:
        - !Sub arn:${AWS::Partition}:lambda:${AWS::Region}:************:layer:AWSLambdaPowertoolsPython:22
        # - arn:aws:lambda:us-east-1:************:layer:doap-boto3rebuild-v1_34:1
      Architectures:
        - x86_64
      Environment:
        Variables:
          SSM_PREFIX: !Ref MonorepoSsmPrefix
          PIPELINE_NAME: !Ref Pipeline
          # APP_DIR_IN_MONOREPO: !Ref ProjectSubfolder
      Policies:
        - CodeCommitReadPolicy:
            RepositoryName: !Ref CodeCommitRepositoryName
        - SSMParameterReadPolicy:
            ParameterName: !Sub "${MonorepoSsmPrefix}/*"
        - Statement:
            - Effect: Allow
              Action: ssm:PutParameter
              Resource: !Sub "arn:${AWS::Partition}:ssm:${AWS::Region}:${AWS::AccountId}:parameter/${MonorepoSsmPrefix}/*"
        - Statement:
            - Effect: Allow
              Action: codepipeline:StartPipelineExecution
              Resource: !Sub "arn:${AWS::Partition}:codepipeline:${AWS::Region}:${AWS::AccountId}:*"
      Events:
        EBRule:
          Type: EventBridgeRule
          Properties:
            DeadLetterConfig:
              Type: SQS
            Pattern:
              source: [aws.codecommit]
              detail-type: ["CodeCommit Repository State Change"]
              resources:
                [
                  !Sub "arn:${AWS::Partition}:codecommit:${AWS::Region}:${AWS::AccountId}:${CodeCommitRepositoryName}",
                ]
              detail:
                event:
                  - referenceCreated
                  - referenceUpdated
                referenceType:
                  - branch
                referenceName:
                  - !Ref MainGitBranch

Outputs:
  CodePipelineUrl:
    Value: !Sub "https://${AWS::Region}.console.aws.amazon.com/codesuite/codepipeline/pipelines/${Pipeline}/view?region=${AWS::Region}"
  CodeBuildServiceRoleArn:
    Value: !GetAtt CodeBuildServiceRole.Arn
  PipelineArtifactsBucketName:
    Value: !Ref PipelineArtifactsBucket
  PipelineArtifactsLoggingBucketName:
    Value: !Ref PipelineArtifactsLoggingBucket
