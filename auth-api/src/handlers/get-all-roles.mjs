import { getAuthOperations } from '../database/index.mjs';
import { STATUSCODE } from '../constants/status-codes.mjs';
import { createResponse } from '../utils/response-utils.mjs';

export const getAllRolesHandler = async (event) => {
  try {
    const authOps = getAuthOperations();
    const roles = await authOps.getAllRoles();
    return createResponse(STATUSCODE.SUCCESS, { roles });
  } catch (error) {
    console.error('Error fetching roles:', error);
    return createResponse(STATUSCODE.SERVER_ERROR, { error: 'Failed to fetch roles' });
  }
}; 