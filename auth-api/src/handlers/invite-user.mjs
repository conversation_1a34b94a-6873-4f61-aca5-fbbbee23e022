import { v4 as uuidv4 } from 'uuid';
import { KMSClient, SignCommand } from '@aws-sdk/client-kms';
import { getAuthOperations } from '../database/index.mjs';
import { STATUSCODE } from '../constants/status-codes.mjs';
import { createResponse } from '../utils/response-utils.mjs';
import { sendEmail } from '/opt/nodejs/index.mjs';

const kms = new KMSClient({});
const KMS_KEY_ID = process.env.USER_INVITATION_SIGNING_KEY_ARN || 'alias/user-invitation-signing-key';
const INVITATION_EXPIRY_HOURS = 48;

export const inviteUserHandler = async (event) => {
  try {
    const body = JSON.parse(event.body || '{}');
    const { email, roles, institute_id } = body;
    if (!email || !Array.isArray(roles) || roles.length === 0) {
      return createResponse(STATUSCODE.BAD_REQUEST, { error: 'email and roles[] are required' });
    }

    const authOps = getAuthOperations();

    // Check if user already exists
    const existingUser = await authOps.getUserByEmail(email);
    if (existingUser) {
      return createResponse(STATUSCODE.CONFLICT, { error: 'User with this email already exists' });
    }

    const id = uuidv4();
    const createdAt = new Date();
    const expireAfter = new Date(Date.now() + INVITATION_EXPIRY_HOURS * 60 * 60 * 1000);
    const payload = `${id}:${email}`;
    const signCmd = new SignCommand({
      KeyId: KMS_KEY_ID,
      Message: Buffer.from(payload),
      SigningAlgorithm: 'RSASSA_PKCS1_V1_5_SHA_256',
      MessageType: 'RAW',
    });
    const signResp = await kms.send(signCmd);
    const signature = Buffer.from(signResp.Signature).toString('base64');

    // Save to DB using authOperations
    await authOps.inviteUser({
      id,
      email,
      institute_id,
      roles,
      created_at: createdAt,
      expire_after: expireAfter
    });

    const invitationLink = `${process.env.INVITATION_BASE_URL}?id=${id}&email=${encodeURIComponent(email)}&sig=${encodeURIComponent(signature)}`;

    // Send the invitation email
    await sendEmail({
      to: email,
      from: process.env.DEFAULT_FROM_EMAIL,
      subject: 'You are invited to join NCICU DOAP',
      message: `Please click the following link to complete your registration: ${invitationLink}`
    });

    return createResponse(STATUSCODE.SUCCESS, { message: 'Invitation sent successfully.' });
  } catch (error) {
    console.error('Error inviting user:', error);
    return createResponse(STATUSCODE.SERVER_ERROR, { error: 'Failed to invite user' });
  }
}; 