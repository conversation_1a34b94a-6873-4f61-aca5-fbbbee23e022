import AWS from 'aws-sdk';
import { getAuthOperations } from '../database/index.mjs';
import { STATUSCODE } from '../constants/status-codes.mjs';
import { createResponse } from '../utils/response-utils.mjs';
import { v4 as uuidv4 } from 'uuid';

const cognito = new AWS.CognitoIdentityServiceProvider();
const USER_POOL_ID = process.env.COGNITO_USER_POOL_ID;

export const createUserHandler = async (event) => {
  try {
    const { name, email, password, invitationId } = JSON.parse(event.body || '{}');
    if (!name || !email || !password || !invitationId) {
      return createResponse(STATUSCODE.BAD_REQUEST, { error: 'name, email, password, and invitationId are required' });
    }
    if (password.length < 8) {
      return createResponse(STATUSCODE.BAD_REQUEST, { error: 'Password must be at least 8 characters' });
    }

    const authOps = getAuthOperations();
    const invitation = await authOps.getValidInvitation(invitationId);
    if (!invitation) {
      return createResponse(STATUSCODE.BAD_REQUEST, { error: 'Invalid or expired invitation' });
    }

    // Create user in Cognito
    await cognito.adminCreateUser({
      UserPoolId: USER_POOL_ID,
      Username: email,
      TemporaryPassword: password,
      UserAttributes: [
        { Name: 'name', Value: name },
        { Name: 'email', Value: email },
        { Name: 'email_verified', Value: 'true' }
      ],
      MessageAction: 'SUPPRESS',
    }).promise();

    await cognito.adminSetUserPassword({
      UserPoolId: USER_POOL_ID,
      Username: email,
      Password: password,
      Permanent: true,
    }).promise();

    // Fetch Cognito sub
    const userInfo = await cognito.adminGetUser({
      UserPoolId: USER_POOL_ID,
      Username: email,
    }).promise();
    const cognitoSub = userInfo.UserAttributes.find(attr => attr.Name === 'sub')?.Value;

    // Create user in local DB
    const newUser = await authOps.createUser({
      id: uuidv4(),
      cognito_sub: cognitoSub,
      name,
      email,
      institute_id: invitation.institute_id
    });
    // Parse roles as array of IDs from comma-separated string
    let roleIds = [];
    if (typeof invitation.roles === 'string') {
      roleIds = invitation.roles.split(',').map(r => r.trim()).filter(Boolean);
    } else if (Array.isArray(invitation.roles)) {
      roleIds = invitation.roles;
    }
    if (!Array.isArray(roleIds) || roleIds.length === 0) {
      return createResponse(STATUSCODE.BAD_REQUEST, { error: 'No roles found in invitation.' });
    }
    await authOps.assignUserRoles(newUser.id, roleIds);
    await authOps.deleteInvitation(invitationId);

    return createResponse(STATUSCODE.CREATED, { user: newUser });
  } catch (error) {
    console.error('Error creating user:', error);
    return createResponse(STATUSCODE.SERVER_ERROR, { error: 'Failed to create user' });
  }
}; 