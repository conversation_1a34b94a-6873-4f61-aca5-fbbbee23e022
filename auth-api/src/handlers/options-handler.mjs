import { createResponse } from "../utils/response-utils.mjs";

/**
 * OPTIONS request handler for CORS preflight requests
 *
 * Why this handler is necessary:
 *
 * 1. CORS Preflight: Browsers send an OPTIONS request before "complex" cross-origin requests
 *    (e.g., POST with JSON, requests with custom headers) to check if the actual request is allowed.
 *
 * 2. Preflight Process:
 *    - <PERSON><PERSON><PERSON> automatically sends OPTIONS request to the same endpoint
 *    - Server must respond with appropriate CORS headers
 *    - Only after a successful preflight will the browser send the actual request
 *
 * 3. Without this handler:
 *    - The preflight OPTIONS request would fail
 *    - <PERSON><PERSON><PERSON> would block the actual request due to CORS policy
 *    - <PERSON><PERSON> would see CORS errors in the console
 *
 * 4. This handler specifically:
 *    - Responds to preflight requests with 200 status code
 *    - Provides necessary CORS headers before the actual request is made
 *    - Tells the browser which methods, headers, and origins are allowed
 */
export const optionsHandler = async () => {
  return createResponse(200, {});
};
