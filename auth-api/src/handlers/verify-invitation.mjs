import { KMSClient, VerifyCommand } from '@aws-sdk/client-kms';
import { getAuthOperations } from '../database/index.mjs';
import { STATUSCODE } from '../constants/status-codes.mjs';
import { createResponse } from '../utils/response-utils.mjs';

const kms = new KMSClient({});
const KMS_KEY_ID = process.env.USER_INVITATION_SIGNING_KEY_ARN || 'alias/user-invitation-signing-key';

export const verifyInvitationHandler = async (event) => {
  try {
    const { id, email, sig: signature } = event.queryStringParameters || {};
    if (!id || !email || !signature) {
      return createResponse(STATUSCODE.BAD_REQUEST, { error: 'id, email, and signature are required' });
    }

    const authOps = getAuthOperations();
    const invitation = await authOps.verifyInvitation({ id, email });
    if (!invitation) {
      return createResponse(STATUSCODE.NOT_FOUND, { error: 'No invitation found for the provided email and id. Please check your link or request a new invitation.' });
    }
    if (invitation.expire_after && new Date(invitation.expire_after) < new Date()) {
      return createResponse(STATUSCODE.BAD_REQUEST, { error: 'Invitation has expired' });
    }
    if (invitation.invitation_accepted) {
      return createResponse(STATUSCODE.CONFLICT, { error: 'Invitation already accepted' });
    }

    // Verify the signature using KMS
    const payload = `${id}:${email}`;
    const verifyCmd = new VerifyCommand({
      KeyId: KMS_KEY_ID,
      Message: Buffer.from(payload),
      Signature: Buffer.from(signature, 'base64'),
      SigningAlgorithm: 'RSASSA_PKCS1_V1_5_SHA_256',
      MessageType: 'RAW',
    });
    const verifyResp = await kms.send(verifyCmd);
    if (!verifyResp.SignatureValid) {
      return createResponse(STATUSCODE.BAD_REQUEST, { error: 'Invalid signature' });
    }

    // Mark invitation as accepted
    await authOps.markInvitationAccepted({ id, email });
    invitation.invitation_accepted = true;
    return createResponse(STATUSCODE.SUCCESS, { valid: true, invitation });
  } catch (error) {
    console.error('Error verifying invitation:', error);
    return createResponse(STATUSCODE.SERVER_ERROR, { error: 'Failed to verify invitation' });
  }
}; 