/**
 * Creates a standardized API response with CORS headers
 *
 * This utility function centralizes response creation across all Lambda handlers,
 * ensuring consistent formatting and CORS headers for every API response.
 *
 * CORS Headers Explanation:
 * - Access-Control-Allow-Origin: Specifies which origins can access the resource
 *   ("*" allows any origin, but can be restricted to specific domains for security)
 * - Access-Control-Allow-Headers: Lists headers the client is allowed to use
 * - Access-Control-Allow-Methods: Lists HTTP methods the client is allowed to use
 * - Content-Type: Sets the response content type to JSON
 *
 * Benefits of this helper:
 * - DRY (Don't Repeat Yourself): Eliminates duplicate CORS header code
 * - Consistency: All responses have the same format and headers
 * - Maintainability: Changes to response format only need to be made in one place
 * - Security: Ensures proper CORS headers are always included
 *
 * @param {number} statusCode - HTTP status code for the response
 * @param {object|array} body - Response body to be JSON stringified
 * @returns {object} - Formatted response object with CORS headers
 */
export const createResponse = (statusCode, body) => {
  return {
    statusCode,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
      "Access-Control-Allow-Methods": "OPTIONS,POST,GET,PUT,DELETE",
      "Content-Type": "application/json"
    },
    body: JSON.stringify(body)
  };
};
