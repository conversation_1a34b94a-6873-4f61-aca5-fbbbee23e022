AWSTemplateFormatVersion: 2010-09-09
Description: >-
  auth-api
Transform:
  - AWS::Serverless-2016-10-31

Parameters:
  Stage:
    Type: String
    Default: dev
    AllowedValues:
      - prod
      - dev
      - qa
    Description: Deployment stage (prod/dev/qa)
  DefaultFromEmail:
    Type: String
    Default: <EMAIL>
    Description: Default sender email address
  CognitoUserPoolId:
    Type: String
    Default: us-east-1_vLrt510U1
    Description: The ID of the existing Cognito User Pool
  CognitoUserPoolArn:
    Type: String
    Default: arn:aws:cognito-idp:us-east-1:211125316254:userpool/us-east-1_vLrt510U1
    Description: The ARN of the existing Cognito User Pool

# Resources declares the AWS resources that you want to include in the stack
Resources:
  # Database layer definition
  DoapDatabaseLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: !Sub "doap-database-layer-${Stage}"
      Description: Common DB logic
      ContentUri: ../common/layer/database
      CompatibleRuntimes:
        - nodejs20.x
      RetentionPolicy: Retain
  
  EmailLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: !Sub "doap-email-layer-${Stage}"
      Description: Email service using AWS SES
      ContentUri: ../common/layer/email
      CompatibleRuntimes:
        - nodejs20.x
      RetentionPolicy: Retain

  AwsSdkLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: !Sub "doap-aws-sdk-layer-${Stage}"
      Description: AWS SDK v2 for Lambda functions
      ContentUri: ../common/layer/aws-sdk
      CompatibleRuntimes:
        - nodejs20.x
      RetentionPolicy: Retain

  UserInvitationSigningKey:
    Type: AWS::KMS::Key
    Properties:
      Description: KMS RSA key for signing user invitation requests
      KeyUsage: SIGN_VERIFY
      KeySpec: RSA_2048
      Enabled: true
      KeyPolicy:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              AWS: !Sub arn:aws:iam::${AWS::AccountId}:root
            Action: 'kms:*'
            Resource: '*'

  UserInvitationSigningKeyAlias:
    Type: AWS::KMS::Alias
    Properties:
      AliasName: alias/user-invitation-signing-key
      TargetKeyId: !Ref UserInvitationSigningKey

  getAllRolesFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/get-all-roles.getAllRolesHandler
      Runtime: nodejs20.x
      Environment:
        Variables:
          DATABASE_URL: !If 
            - IsProd
            - "postgres://doapadmin:<EMAIL>:5432/doapProdDb"
            - "postgres://doapadmin:<EMAIL>:5432/doapDb"
      Architectures:
        - x86_64
      MemorySize: 128
      Timeout: 30
      Description: Get all user roles present in the system
      Layers:
        - !Ref DoapDatabaseLayer
      Events:
        Api:
          Type: Api
          Properties:
            Path: /auth/roles
            Method: GET
            RestApiId: !Ref ApiGateway

  optionsFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/options-handler.optionsHandler
      Runtime: nodejs20.x
      Architectures:
        - x86_64
      MemorySize: 128
      Timeout: 100
      Description: Handles OPTIONS requests for CORS preflight
      Layers:
        - !Ref DoapDatabaseLayer
      Events:
        ApiRoles:
          Type: Api
          Properties:
            Path: /auth/roles
            Method: OPTIONS
            RestApiId: !Ref ApiGateway
        ApiInviteUser:
          Type: Api
          Properties:
            Path: /auth/invite-user
            Method: OPTIONS
            RestApiId: !Ref ApiGateway
        ApiVerifyInvite:
          Type: Api
          Properties:
            Path: /auth/verify-invite
            Method: OPTIONS
            RestApiId: !Ref ApiGateway

  inviteUserFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/invite-user.inviteUserHandler
      Runtime: nodejs20.x
      Environment:
        Variables:
          DATABASE_URL: !If 
            - IsProd
            - "postgres://doapadmin:<EMAIL>:5432/doapProdDb"
            - "postgres://doapadmin:<EMAIL>:5432/doapDb"
          USER_INVITATION_SIGNING_KEY_ARN: !GetAtt UserInvitationSigningKey.Arn
          INVITATION_BASE_URL: !If
            - IsProd
            - "https://portal.ncicudoap.com/signup"
            - !Sub "https://portal.${Stage}.ncicudoap.com/signup"
          DEFAULT_FROM_EMAIL: !Ref DefaultFromEmail
      Architectures:
        - x86_64
      MemorySize: 128
      Timeout: 30
      Description: Invite a user by email and roles, generate a signed invitation link, and save to DB
      Layers:
        - !Ref DoapDatabaseLayer
        - !Ref EmailLayer
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - kms:Sign
              Resource: !GetAtt UserInvitationSigningKey.Arn
        - Statement:
            - Effect: Allow
              Action:
                - ses:SendEmail
                - ses:SendRawEmail
              Resource: '*'
      Events:
        Api:
          Type: Api
          Properties:
            Path: /auth/invite-user
            Method: POST
            RestApiId: !Ref ApiGateway

  verifyInvitationFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/verify-invitation.verifyInvitationHandler
      Runtime: nodejs20.x
      Environment:
        Variables:
          DATABASE_URL: !If 
            - IsProd
            - "postgres://doapadmin:<EMAIL>:5432/doapProdDb"
            - "postgres://doapadmin:<EMAIL>:5432/doapDb"
          USER_INVITATION_SIGNING_KEY_ARN: !GetAtt UserInvitationSigningKey.Arn
      Architectures:
        - x86_64
      MemorySize: 128
      Timeout: 30
      Description: Verify a user invitation using KMS and DB
      Layers:
        - !Ref DoapDatabaseLayer
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - kms:Verify
              Resource: !GetAtt UserInvitationSigningKey.Arn
      Events:
        Api:
          Type: Api
          Properties:
            Path: /auth/verify-invite
            Method: GET
            RestApiId: !Ref ApiGateway

  createUserFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/create-user.createUserHandler
      Runtime: nodejs20.x
      Environment:
        Variables:
          DATABASE_URL: !If 
            - IsProd
            - "postgres://doapadmin:<EMAIL>:5432/doapProdDb"
            - "postgres://doapadmin:<EMAIL>:5432/doapDb"
          COGNITO_USER_POOL_ID: !If [IsProd, 1_vLrt510U2, us-east-1_vLrt510U1]
      Architectures:
        - x86_64
      MemorySize: 128
      Timeout: 30
      Description: Create a new user after verifying an invitation
      Layers:
        - !Ref DoapDatabaseLayer
        - !Ref AwsSdkLayer
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - cognito-idp:AdminCreateUser
                - cognito-idp:AdminSetUserPassword
                - cognito-idp:AdminGetUser
              Resource: !If [IsProd, !Sub 'arn:aws:cognito-idp:us-east-1:${AWS::AccountId}:userpool/1_vLrt510U2', !Sub 'arn:aws:cognito-idp:us-east-1:${AWS::AccountId}:userpool/us-east-1_vLrt510U1']
      Events:
        Api:
          Type: Api
          Properties:
            Path: /auth/create-user
            Method: POST
            RestApiId: !Ref ApiGateway

  ApplicationResourceGroup:
    Type: AWS::ResourceGroups::Group
    Properties:
      Name:
        Fn::Sub: ApplicationInsights-SAM-${AWS::StackName}
      ResourceQuery:
        Type: CLOUDFORMATION_STACK_1_0

  ApplicationInsightsMonitoring:
    Type: AWS::ApplicationInsights::Application
    Properties:
      ResourceGroupName:
        Ref: ApplicationResourceGroup
      AutoConfigurationEnabled: true

  ApiGateway:
    Type: AWS::Serverless::Api
    Properties:
      StageName: !Ref Stage
      EndpointConfiguration: 
        Type: EDGE
      TracingEnabled: true
      # Remove the MethodSettings section entirely to avoid logging configuration

Conditions:
  IsProd: !Equals [!Ref Stage, "prod"]
  IsDev: !Equals [!Ref Stage, "dev"]
  IsQa: !Equals [!Ref Stage, "qa"]

Outputs:
  ApiGatewayRestApiId:
    Description: The ID of the API Gateway created by SAM
    Value: !Ref ApiGateway
    Export:
      Name: !Sub "${AWS::StackName}-ApiGatewayRestApiId-${Stage}"
  WebEndpoint:
    Description: API Gateway endpoint URL for the specified stage
    Value: !Sub "https://${ApiGateway}.execute-api.${AWS::Region}.amazonaws.com/${Stage}/"
  verifyInvitationFunctionArn:
    Description: ARN of the verify invitation Lambda function
    Value: !GetAtt verifyInvitationFunction.Arn
    Export:
      Name: !Sub "${AWS::StackName}-VerifyInvitationFunctionArn-${Stage}"
  VerifyInvitationApiEndpoint:
    Description: API endpoint for verifying user invitations
    Value: !Sub "https://${ApiGateway}.execute-api.${AWS::Region}.amazonaws.com/${Stage}/auth/verify-invite"
    Export:
      Name: !Sub "${AWS::StackName}-VerifyInvitationApiEndpoint-${Stage}"

Globals:
  Function:
    Tracing: Active
    LoggingConfig:
      LogFormat: JSON
  Api:
    TracingEnabled: true
