AWSTemplateFormatVersion: 2010-09-09
Description: API Gateway Documentation Parts

Parameters:
  Environment:
    Type: String
    Default: Dev
    AllowedValues:
      - Dev
      - Prod
    Description: Environment name (Dev/Prod)

Resources:
  DocumentationPartInstitutes:
    Type: AWS::ApiGateway::DocumentationPart
    Properties:
      RestApiId: !ImportValue 
        Fn::Sub: ${AWS::StackName}-ApiGatewayRestApiId
      Location:
        Method: GET
        Path: /institutes
      Properties: |
        {
          "description": "This endpoint retrieves all institutes.",
          "summary": "Get all institutes"
        }

  DocumentationPartInstituteById:
    Type: AWS::ApiGateway::DocumentationPart
    Properties:
      RestApiId: !ImportValue 
        Fn::Sub: ${AWS::StackName}-ApiGatewayRestApiId
      Location:
        Method: GET
        Path: /institutes/{id}
      Properties: |
        {
          "description": "This endpoint retrieves an institute by its ID.",
          "summary": "Get institute by ID"
        }