{"getAllItemsFunction": {"DATABASE_URL": "postgres://doapadmin:<EMAIL>:5432/doapDb"}, "getByIdFunction": {"DATABASE_URL": "postgres://doapadmin:<EMAIL>:5432/doapDb"}, "putItemFunction": {"DATABASE_URL": "postgres://doapadmin:<EMAIL>:5432/doapDb"}, "optionsFunction": {"DATABASE_URL": "postgres://doapadmin:<EMAIL>:5432/doapDb"}, "SubmitCollectionInstituteHistoryFormFunction": {"FORMS_BUCKET_NAME": "doap-forms-bucket-211125316254-dev"}}