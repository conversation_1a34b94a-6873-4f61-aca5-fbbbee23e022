import { collectionOperations } from '../database/index.mjs';
import { createResponse } from '../utils/response-utils.mjs';
import { STATUSCODE } from '../constants/status-codes.mjs';
import { processOverallStatus } from '../utils/status-processor.mjs';

/**
 * Parse comma-separated string into array of integers
 * @param {string} value - Comma-separated string of integers
 * @returns {Array<number>} Array of parsed integers
 */
const parseCommaSeparatedIds = (value) => {
  if (!value) return null;
  
  try {
    return value.split(',')
      .map(id => id.trim())
      .filter(id => id.length > 0)
      .map(id => parseInt(id, 10))
      .filter(id => !isNaN(id));
  } catch (error) {
    console.error('Error parsing comma-separated IDs:', error);
    return null;
  }
};

/**
 * Parse comma-separated string into array of strings
 * @param {string} value - Comma-separated string
 * @returns {Array<string>} Array of parsed strings
 */
const parseCommaSeparatedStrings = (value) => {
  if (!value) return null;
  
  try {
    return value.split(',')
      .map(item => item.trim())
      .filter(item => item.length > 0);
  } catch (error) {
    console.error('Error parsing comma-separated strings:', error);
    return null;
  }
};

/**
 * Parse filter parameters from query string parameters
 * @param {Object} queryParams - Query string parameters
 * @param {number} instituteId - The institute ID from path parameters
 * @returns {Object} Filter parameters
 */
const parseFilterParams = (queryParams, instituteId) => {
  const filters = { institute_id: instituteId };

  // Name filter for collection name
  if (queryParams?.name) {
    filters.name = queryParams.name.trim();
  }

  // Collection filter - supports comma-separated values
  if (queryParams?.collection) {
    const collectionIds = parseCommaSeparatedIds(queryParams.collection);
    if (collectionIds && collectionIds.length > 0) {
      filters.collection_id = collectionIds.length === 1 ? collectionIds[0] : collectionIds;
    }
  }

  // History filter - supports comma-separated values
  if (queryParams?.history) {
    const historyIds = parseCommaSeparatedIds(queryParams.history);
    if (historyIds && historyIds.length > 0) {
      filters.collection_history_id = historyIds.length === 1 ? historyIds[0] : historyIds;
    }
  }

  // Status filter - supports comma-separated values
  if (queryParams?.status) {
    const statusIds = parseCommaSeparatedIds(queryParams.status);
    if (statusIds && statusIds.length > 0) {
      filters.collection_status_id = statusIds.length === 1 ? statusIds[0] : statusIds;
    }
  }

  // Year filter
  if (queryParams?.year) {
    filters.year = parseInt(queryParams.year, 10);
  }

  return filters;
};

/**
 * Parse pagination parameters from query string
 * @param {Object} queryParams - Query string parameters
 * @returns {Object} Pagination parameters
 */
const parsePaginationParams = (queryParams) => {
  const page = queryParams?.page ? parseInt(queryParams.page, 10) : 1;
  const pageSize = queryParams?.pageSize ? parseInt(queryParams.pageSize, 10) : 10;
  
  return {
    page: Math.max(1, page),
    pageSize: Math.max(1, Math.min(1000, pageSize)), // Cap at 1000 items per page
    offset: (page - 1) * pageSize
  };
};

/**
 * Handler for getting collection summaries for a specific institute
 * @param {Object} event - The event object from API Gateway
 * @returns {Object} Response object
 */
export const getInstituteCollectionSummariesHandler = async (event) => {
  try {
    // Extract path parameters
    const { institute } = event.pathParameters || {};
    if (!institute) {
      return createResponse(STATUSCODE.BAD_REQUEST, {
        message: 'instituteId is required in path'
      });
    }

    // Parse filter parameters
    const filter = parseFilterParams(event.queryStringParameters || {}, parseInt(institute));

    // Parse pagination parameters
    const pagination = parsePaginationParams(event.queryStringParameters || {});

    // Get results without pagination
    const results = await collectionOperations.collectionInstituteHistory.getPaginated(
      null,
      null,
      filter
    );

    // Build a map grouped by collection_id and history_id
    const groupedSummaries = {};
    for (const result of results) {
      const key = `${result.collection_id}:${result.collection_history_id}`;
      if (!groupedSummaries[key]) {
        groupedSummaries[key] = [];
      }
      groupedSummaries[key].push(result);
    }

    // Calculate overall_status for each group and store in a new map
    const overallStatusMap = {};
    for (const key in groupedSummaries) {
      const group = groupedSummaries[key];
      overallStatusMap[key] = processOverallStatus(group);
    }

    // Map to summary output
    const summaries = results.map(item => {
      const key = `${item.collection_id}:${item.collection_history_id}`;
      return {
        collection_id: item.collection_id,
        history_id: item.collection_history_id,
        collection_name: item.collection_name,
        collection_status_id: item.collection_status_id,
        open_date: item.open_date,
        close_date: item.close_date,
        collection_type_id: item.collection_type_id,
        collection_window_id: item.collection_window_id,
        year: item.execution_date ? new Date(item.execution_date).getFullYear() : null,
        overall_status: overallStatusMap[key]
      };
    });

    // De-duplicate summaries to ensure unique collectionId and historyId pairs
    const seen = new Set();
    const uniqueSummaries = summaries.filter(summary => {
      const key = `${summary.collection_id}:${summary.history_id}`;
      if (!seen.has(key)) {
        seen.add(key);
        return true;
      }
      return false;
    });

     // Apply overall_status filter if provided
    let filteredSummaries = uniqueSummaries;
    if (event.queryStringParameters?.overall_status) {
      const statusFilters = parseCommaSeparatedStrings(event.queryStringParameters.overall_status);
      if (statusFilters && statusFilters.length > 0) {
        filteredSummaries = uniqueSummaries.filter(s => 
          statusFilters.some(filter => s.overall_status.toLowerCase() === filter.toLowerCase())
        );
      }
    }
    
    // Apply pagination to filtered summaries
    const totalCount = filteredSummaries.length;
    const paginatedSummaries = filteredSummaries.slice(
      pagination.offset,
      pagination.offset + pagination.pageSize
    );

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / pagination.pageSize);
    const hasNextPage = pagination.page < totalPages;
    const hasPrevPage = pagination.page > 1;

    // Log response details
    console.info(
      `Response from: ${event.path} statusCode: ${STATUSCODE.SUCCESS} numResults: ${paginatedSummaries.length} page: ${pagination.page} totalPages: ${totalPages}`
    );

    return createResponse(STATUSCODE.SUCCESS, {
      histories: paginatedSummaries,
      pagination: {
        page: pagination.page,
        pageSize: pagination.pageSize,
        totalCount,
        totalPages,
        hasNextPage,
        hasPrevPage
      }
    });

  } catch (error) {
    // Log the error
    console.error('Error in getInstituteCollectionSummariesHandler:', error);

    // Return appropriate error response
    return createResponse(
      error.statusCode || STATUSCODE.SERVER_ERROR,
      { 
        message: error.message || 'Error retrieving collection summaries for institute',
        error: error.toString()
      }
    );
  }
}; 