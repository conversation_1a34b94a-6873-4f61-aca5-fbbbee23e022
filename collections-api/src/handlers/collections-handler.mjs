import { collectionOperations } from '../database/index.mjs';
import { CollectionStatus, CollectionWindow } from '../utils/enums.mjs';
import { createNextYearPendingCollectionHistory, createCollectionInstituteHistories, updateResults } from '../utils/collection-institute-history-utils.mjs';

/**
 * Handler for processing collections
 * This handler uses only the database layer to perform operations
 * @param {Object} event - The event object from CloudWatch Events
 * @returns {Object} Response object
 */
export const collectionsHandler = async (event) => {
  console.log('Event:', JSON.stringify(event, null, 2));

  try {
    // Get previous year and current year
    const previousYear = (new Date().getFullYear() - 1).toString();
    const currentYear = new Date().getFullYear().toString();
    const currentDate = new Date();

    console.log(`Previous Year = ${previousYear}`);
    
    // Get all collection histories with statusId 2 from previous year
    const closedCollectionHistories = await collectionOperations.collectionHistory.getPaginated(
      0, // page
      100000, // limit
      { 
        year: previousYear,
        statusId: CollectionStatus.Closed, // closed status
        windowId: CollectionWindow.Recurring
      }
    );

    console.log(`Found ${closedCollectionHistories.length} closed collections from ${previousYear}`);

    // Get all active collections from current year
    const activeCollections = await collectionOperations.collectionHistory.getPaginated(
      0, // page
      100000, // limit
      { 
        year: currentYear,
        statusId: CollectionStatus.Active // active status
      }
    );

    console.log(`Found ${activeCollections.length} active collections from ${currentYear}`);

    // Get all pending collections from current year
    const pendingCollections = await collectionOperations.collectionHistory.getPaginated(
      0, // page
      100000, // limit
      { 
        year: currentYear,
        statusId: CollectionStatus.Pending // pending status
      }
    );

    console.log(`Found ${pendingCollections.length} pending collections from ${currentYear}`);

    const results = {
      processed: 0,
      updated: 0,
      created: 0,
      errors: 0,
      details: []
    };

    // Process closed collections from previous year
    for (const collection of closedCollectionHistories) {
      try {
        // Calculate open_date and close_date for the current year
        const origOpenDate = new Date(collection.open_date);
        const origCloseDate = new Date(collection.close_date);
        const year = currentDate.getFullYear();
        const openDate = new Date(year, origOpenDate.getMonth(), origOpenDate.getDate());
        const closeDate = new Date(year, origCloseDate.getMonth(), origCloseDate.getDate());

        // Check if collection already has an active history in current year
        const existingActiveHistory = await collectionOperations.collectionHistory.getPaginated(
          0, // page
          1, // limit
          {
            year: currentYear,
            collectionId: collection.collection_id
          }
        );

        if (existingActiveHistory.length > 0) {
          console.log(`Collection ${collection.collection_id} already has an active history in ${currentYear}, skipping...`);
          results.processed++;
          continue;
        }

        if (currentDate >= openDate && currentDate <= closeDate) {
          // Create new history record with Active status
          const historyRecord = await collectionOperations.collectionHistory.create({
            collection_id: collection.collection_id,
            collection_status_id: CollectionStatus.Active, // Active status
            execution_date: currentDate,
            open_date: openDate,
            close_date: closeDate
          });

          // Create collection institute history records
          const { forms, institutes } = await createCollectionInstituteHistories(
            historyRecord, 
            collection.collection_id, 
            currentDate
          );

          // Update results
          updateResults(
            results,
            collection,
            collection.collection_status_id,
            CollectionStatus.Active,
            openDate,
            closeDate,
            currentDate,
            'created',
            forms,
            institutes
          );

          console.log(`Created new history record for collection ${collection.collection_id} with Active status`);
        }

        results.processed++;

      } catch (collectionError) {
        console.error(`Error processing collection ${collection.collection_id}:`, collectionError);
        results.errors++;
      }
    }

    // Process active collections from current year
    for (const collection of activeCollections) {
      try {
        const closeDate = collection.close_date ? new Date(collection.close_date) : null;

        // Check if collection needs to be closed
        if (closeDate && currentDate > closeDate) {
          // Update collection status to closed
          await collectionOperations.collectionHistory.update(collection.id, {
            ...collection,
            collection_status_id: CollectionStatus.Closed, // Closed status
            execution_date: currentDate
          });

          // Also mark all related collection institute histories as closed
          const instituteHistories = await collectionOperations.collectionInstituteHistory.getPaginated(
            0, // offset
            10000, // large limit
            {
              collection_id: collection.collection_id,
              collection_history_id: collection.id
            }
          );
          for (const hist of instituteHistories) {
            // Only mark as Overdue if not Submitted, Accepted, or Rejected
            if (
              hist.collection_status_id !== CollectionStatus.Submitted &&
              hist.collection_status_id !== CollectionStatus.Accepted &&
              hist.collection_status_id !== CollectionStatus.Rejected
            ) {
              await collectionOperations.collectionInstituteHistory.update(hist.id, {
                collection_status_id: CollectionStatus.Overdue,
                execution_date: currentDate
              });
            }
          }

          // --- Begin: Create new collection history with Pending status for next year ---
          await createNextYearPendingCollectionHistory(collection);
          // --- End: Create new collection history with Pending status for next year ---

          // Update results
          updateResults(
            results,
            collection,
            collection.collection_status_id,
            CollectionStatus.Closed,
            collection.open_date,
            collection.close_date,
            currentDate,
            'updated'
          );

          console.log(`Updated collection ${collection.collection_id} status from Active to Closed`);
        } else if (!closeDate) {
          // Adhoc collection without close date - keep it active
          console.log(`Adhoc collection ${collection.collection_id} has no close date, keeping it active`);
        }

        results.processed++;

      } catch (collectionError) {
        console.error(`Error processing collection ${collection.collection_id}:`, collectionError);
        results.errors++;
      }
    }

    // Process pending collections from current year
    for (const collection of pendingCollections) {
      try {
        const openDate = new Date(collection.open_date);
        const closeDate = collection.close_date ? new Date(collection.close_date) : null;

        // Check if collection needs to be activated
        if (closeDate) {
          // Collection with both open and close dates
          if (currentDate >= openDate && currentDate <= closeDate) {
            // Update collection status to active
            const historyRecord = await collectionOperations.collectionHistory.update(collection.id, {
              ...collection,
              collection_status_id: CollectionStatus.Active, // Active status
              execution_date: currentDate
            });

            // Create collection institute history records
            const { forms, institutes } = await createCollectionInstituteHistories(
              historyRecord, 
              collection.collection_id, 
              currentDate
            );

            // Update results
            updateResults(
              results,
              collection,
              collection.collection_status_id,
              CollectionStatus.Active,
              collection.open_date,
              collection.close_date,
              currentDate,
              'updated',
              forms,
              institutes
            );

            console.log(`Updated collection ${collection.collection_id} status from Pending to Active`);
          }
        } else {
          // Adhoc collection without close date - activate if current date is after open date
          if (currentDate >= openDate) {
            // Update collection status to active
            const historyRecord = await collectionOperations.collectionHistory.update(collection.id, {
              ...collection,
              collection_status_id: CollectionStatus.Active, // Active status
              execution_date: currentDate
            });

            // Create collection institute history records
            const { forms, institutes } = await createCollectionInstituteHistories(
              historyRecord, 
              collection.collection_id, 
              currentDate
            );

            // Update results
            updateResults(
              results,
              collection,
              collection.collection_status_id,
              CollectionStatus.Active,
              collection.open_date,
              null,
              currentDate,
              'updated',
              forms,
              institutes
            );

            console.log(`Updated adhoc collection ${collection.collection_id} status from Pending to Active`);
          }
        }

        results.processed++;

      } catch (collectionError) {
        console.error(`Error processing collection ${collection.collection_id}:`, collectionError);
        results.errors++;
      }
    }

    // Log final results
    console.info(
      `Collections processing completed - Processed: ${results.processed} collections, ` +
      `Updated: ${results.updated}, Created: ${results.created}, Errors: ${results.errors}`
    );
    console.info('Processing details:', JSON.stringify(results.details, null, 2));

  } catch (error) {
    console.error('Error in collections handler:', error);
  }

  // Always return OK response
  return {
    statusCode: 200,
    body: JSON.stringify({
      message: 'Collections processing completed',
      timestamp: new Date().toISOString()
    })
  };
}; 