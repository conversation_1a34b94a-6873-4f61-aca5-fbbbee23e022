import {
  STATUSCODE
} from "../constants/status-codes.mjs";
import { collectionOperations } from "../database/index.mjs";
import { createResponse } from "../utils/response-utils.mjs";

/**
 * Handler for GET /forms endpoint
 * Retrieves all template forms from the database
 */
export const getAllFormsHandler = async (event) => {
  try {
    // Query database for all forms
    const forms = await collectionOperations.form.getAll();

    // Filter to only include template forms
    const templateForms = forms.filter(form => form.is_template === true);

    // Create successful response
    const response = createResponse(STATUSCODE.SUCCESS, templateForms);

    // Log response details
    console.info(
      `Response from: ${event.path} statusCode: ${response.statusCode} numTemplateForms: ${templateForms?.length}`
    );

    return response;
  } catch (err) {
    // Log the error
    console.error("Error retrieving template forms:", err);

    // Return appropriate error response
    const errorResponse = createResponse(
      err.statusCode || STATUSCODE.SERVER_ERROR,
      { message: err.message || "Error retrieving template forms", error: err.toString() }
    );

    console.info(
      `Error response from: ${event.path} statusCode: ${errorResponse.statusCode} body: ${errorResponse.body}`
    );

    return errorResponse;
  }
};
