import {
  STATUSCODE
} from "../constants/status-codes.mjs";
import { collectionOperations } from "../database/index.mjs";
import { createResponse } from "../utils/response-utils.mjs";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

// Initialize S3 client
const s3Client = new S3Client({ region: process.env.AWS_REGION || 'us-east-1' });

/**
 * Generate a presigned URL for uploading a form file
 * @param {number} formId - The form ID
 * @param {string} fileName - The file name
 * @returns {Promise<string>} Presigned URL
 */
const generateUploadUrl = async (formId, fileName) => {
  const fileKey = `${formId}/${fileName}`;
  
  const command = new PutObjectCommand({
    Bucket: process.env.FORMS_BUCKET_NAME,
    Key: fileKey,
    ContentType: 'application/pdf'
  });

  // Generate presigned URL that expires in 1 hour
  return await getSignedUrl(s3Client, command, { expiresIn: 3600 });
};

/**
 * Handler for POST /collections/forms endpoint
 * Creates new forms and returns presigned URLs for file uploads
 */
export const uploadFormsHandler = async (event) => {
  try {
    // Parse request body
    const body = JSON.parse(event.body);
    if (!Array.isArray(body)) {
      return createResponse(STATUSCODE.BAD_REQUEST, { 
        message: "Request body must be an array of form objects" 
      });
    }

    const results = await Promise.all(body.map(async (formData) => {
      const { id, name, description, is_template, file_name } = formData;

      // Validate required fields
      if (!name || !file_name) {
        return createResponse(STATUSCODE.BAD_REQUEST, {
          success: false,
          error: "Name and file_name are required fields"
        });
      }

      try {
        let form;
        if (id) {
          // Check if form with the provided ID exists
          const existingForm = await collectionOperations.form.getById(id);
          if (!existingForm) {
            return createResponse(STATUSCODE.NOT_FOUND, {
              success: false,
              error: `Form with ID ${id} not found`
            });
          }
          
          // Update existing form
          form = await collectionOperations.form.update(id, {
            name,
            description,
            is_template,
            file_name
          });
        } else {
          // Create new form
          form = await collectionOperations.form.create({
            name,
            description,
            is_template,
            file_name
          });
        }
        // Generate presigned URL for file upload
        const uploadUrl = await generateUploadUrl(form.id, file_name);

        return createResponse(STATUSCODE.SUCCESS, {
          success: true,
          form,
          upload_url: uploadUrl
        });
      } catch (error) {
        console.error(`Error processing form ${name}:`, error);
        return createResponse(STATUSCODE.SERVER_ERROR, {
          success: false,
          error: error.message
        });
      }
    }));

    // Create successful response
    const response = createResponse(STATUSCODE.SUCCESS, {
      message: "Forms processed successfully",
      results
    });

    // Log response details
    console.info(
      `Response from: ${event.path} statusCode: ${response.statusCode} numForms: ${body.length}`
    );

    return response;
  } catch (err) {
    // Log the error
    console.error("Error processing forms:", err);

    // Return appropriate error response
    const errorResponse = createResponse(
      err.statusCode || STATUSCODE.SERVER_ERROR,
      { message: err.message || "Error processing forms", error: err.toString() }
    );

    console.info(
      `Error response from: ${event.path} statusCode: ${errorResponse.statusCode} body: ${errorResponse.body}`
    );

    return errorResponse;
  }
}; 