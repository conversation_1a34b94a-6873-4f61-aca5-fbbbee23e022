import { collectionOperations } from '../database/index.mjs';
import { createResponse, validateJsonAgainstSchema } from '../utils/response-utils.mjs';
import { STATUSCODE } from '../constants/status-codes.mjs';
import { CollectionStatus } from '../utils/enums.mjs';
import { createNextYearPendingCollectionHistory } from '../utils/collection-institute-history-utils.mjs';

/**
 * <PERSON><PERSON> for saving a form draft
 * @param {Object} event - The event object from API Gateway
 * @returns {Object} Response object
 */
export const updateFormStatusHandler = async (event) => {
  try {
    // Extract record ID from path parameters
    const recordId = event.pathParameters?.id;

    if (!recordId) {
      return createResponse(
        STATUSCODE.BAD_REQUEST,
        { message: 'Record ID is required' }
      );
    }

    // Parse request body
    let requestBody;
    try {
      requestBody = JSON.parse(event.body || '{}');
    } catch (parseError) {
      return createResponse(
        STATUSCODE.BAD_REQUEST,
        { message: 'Invalid JSON in request body' }
      );
    }

    // Extract collection_status_id from body
    const { collection_status_id } = requestBody;
    if (collection_status_id === undefined) {
      return createResponse(
        STATUSCODE.BAD_REQUEST,
        { message: 'collection_status_id is required in the request body' }
      );
    }

    // Validate that the collection_status_id exists in the collection_status table
    const validStatus = await collectionOperations.collectionStatus.getById(collection_status_id);
    if (!validStatus) {
      return createResponse(
        STATUSCODE.BAD_REQUEST,
        { message: `collection_status_id ${collection_status_id} is not valid.` }
      );
    }

    // Check if the record exists
    const existingRecord = await collectionOperations.collectionInstituteHistory.getById(recordId);
    if (!existingRecord) {
      return createResponse(
        STATUSCODE.NOT_FOUND,
        { message: 'Collection institute history record not found' }
      );
    }

    // Prepare update data
    const updateData = {
      collection_status_id,
      execution_date: new Date()
    };

    // Update the record
    const updatedRecord = await collectionOperations.collectionInstituteHistory.update(recordId, updateData);

    if (!updatedRecord) {
      return createResponse(
        STATUSCODE.SERVER_ERROR,
        { message: 'Failed to update form status' }
      );
    }

    // If the new status is Accepted, check if all forms for this collection history are Accepted
    if (collection_status_id === CollectionStatus.Accepted) {
      const collection_history_id = updatedRecord.collection_history_id;
      // Get all forms for this collection history
      const allForms = await collectionOperations.collectionInstituteHistory.getPaginated(
        0,
        10000,
        { collection_history_id }
      );
      // If all forms are Accepted, close the collection history
      if (allForms.every(f => f.collection_status_id === CollectionStatus.Accepted)) {
        const closedHistory = await collectionOperations.collectionHistory.update(collection_history_id, {
          collection_status_id: CollectionStatus.Closed,
          execution_date: new Date()
        });
        // Create a pending collection for the next year
        await createNextYearPendingCollectionHistory(closedHistory);
      }
    }

    // Get the updated record with full details
    const fullRecord = await collectionOperations.collectionInstituteHistory.getById(recordId);

    return createResponse(
      STATUSCODE.SUCCESS,
      {
        message: 'Form status updated successfully',
        record: fullRecord
      }
    );
  } catch (error) {
    console.error('Error in updateFormStatusHandler:', error);
    return createResponse(
      error.statusCode || STATUSCODE.SERVER_ERROR,
      { 
        message: error.message || 'Error updating form status',
        error: error.toString()
      }
    );
  }
}; 