import { collectionOperations } from '../database/index.mjs';
import { createResponse } from '../utils/response-utils.mjs';
import { STATUSCODE } from '../constants/status-codes.mjs';
import { processOverallStatus } from '../utils/status-processor.mjs';

/**
 * <PERSON><PERSON> for getting all institutes' collection history
 * @param {Object} event - The event object from API Gateway
 * @returns {Object} Response object
 */
export const getAllInstitutesCollectionHistoryHandler = async (event) => {
  try {
    // Extract historyId from path parameters
    const historyId = event.pathParameters?.id;

    if (!historyId) {
      return createResponse(
        STATUSCODE.BAD_REQUEST,
        { message: 'History ID is required' }
      );
    }

    // Get all institutes' history for the specified history ID
    const history = await collectionOperations.collectionInstituteHistory.getInstitutesCollectionHistory(historyId);

    if (!history || history.length === 0) {
      return createResponse(
        STATUSCODE.NOT_FOUND,
        { message: 'No history found for the specified history ID' }
      );
    }

    // Group records by institute
    const groupedByInstitute = history.reduce((acc, record) => {
      if (!record || !record.institute_id) {
        console.warn('Skipping invalid record:', record);
        return acc;
      }

      const instituteKey = record.institute_id;
      
      if (!acc[instituteKey]) {
        acc[instituteKey] = {
          institute_id: record.institute_id,
          institute_name: record.institute_name || '',
          institute_email: record.institute_email || '',
          institute_contact: record.institute_contact || '',
          forms: []
        };
      }

      // Add form information
      acc[instituteKey].forms.push({
        id: record.id,
        form_id: record.form_id || null,
        template_form_id: record.template_form_id || null,
        form_name: record.form_name || '',
        template_form_name: record.template_form_name || '',
        status_name: record.status_name || '',
        collection_status_id: record.collection_status_id || null
      });

      return acc;
    }, {});

    // Get collection details from the first record (they're all the same for a given history)
    const closeDate = history[0]?.close_date || null;
    const now = new Date();
    const statusName = history[0]?.status_name || '';
    let days_left = null;
    if (statusName === 'Submitted') {
      days_left = 0;
    } else if (closeDate) {
      const diff = Math.ceil((closeDate - now) / (1000 * 60 * 60 * 24));
      days_left = diff > 0 ? diff : 0;
    }
    const year = history[0]?.execution_date ? new Date(history[0].execution_date).getFullYear() : null;
    const collectionDetails = {
      collection_id: history[0]?.collection_id || null,
      collection_name: history[0]?.collection_name || '',
      collection_description: history[0]?.collection_description || '',
      open_date: history[0]?.open_date || null,
      close_date: history[0]?.close_date || null,
      collection_type_id: history[0]?.collection_type_id || null,
      collection_window_id: history[0]?.collection_window_id || null,
      days_left,
      year
    };

    // Process overall status for each institute
    const institutes = Object.values(groupedByInstitute).map(institute => ({
      ...institute,
      overall_status: processOverallStatus(institute.forms)
    }));

    // Log response details
    console.info(
      `Response from: ${event.path} statusCode: ${STATUSCODE.SUCCESS} numInstitutes: ${institutes.length}`
    );

    return createResponse(
      STATUSCODE.SUCCESS,
      {
        ...collectionDetails,
        institutes
      }
    );

  } catch (error) {
    // Log the error
    console.error('Error in getAllInstitutesCollectionHistoryHandler:', error);

    // Return appropriate error response
    return createResponse(
      error.statusCode || STATUSCODE.SERVER_ERROR,
      { 
        message: error.message || 'Error retrieving institutes collection history',
        error: error.toString()
      }
    );
  }
}; 