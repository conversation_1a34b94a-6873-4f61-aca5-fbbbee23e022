import {
  STATUSCODE
} from "../constants/status-codes.mjs";
import { collectionOperations } from "../database/index.mjs";
import { createResponse } from "../utils/response-utils.mjs";

/**
 * Handler for GET /forms/{id} endpoint
 * Retrieves a specific form by ID from the database
 */
export const getFormByIdHandler = async (event) => {
  try {
    // Get form ID from path parameters
    const formId = event.pathParameters?.id;
    if (!formId) {
      return createResponse(STATUSCODE.BAD_REQUEST, { message: "Missing form ID" });
    }

    // Query database for the specific form
    const form = await collectionOperations.form.getById(parseInt(formId, 10));

    // Check if form was found
    if (!form) {
      return createResponse(STATUSCODE.NOT_FOUND, { message: `Form with ID ${formId} not found` });
    }

    // Create successful response
    const response = createResponse(STATUSCODE.SUCCESS, form);

    // Log response details
    console.info(
      `Response from: ${event.path} statusCode: ${response.statusCode} form: ${form?.name}`
    );

    return response;
  } catch (err) {
    // Log the error
    console.error(`Error retrieving form with ID ${event.pathParameters?.id}:`, err);

    // Return appropriate error response
    const errorResponse = createResponse(
      err.statusCode || STATUSCODE.SERVER_ERROR,
      { message: err.message || "Error retrieving form", error: err.toString() }
    );

    console.info(
      `Error response from: ${event.path} statusCode: ${errorResponse.statusCode} body: ${errorResponse.body}`
    );

    return errorResponse;
  }
}; 