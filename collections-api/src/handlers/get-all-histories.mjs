import {
  STATUSCODE
} from "../constants/status-codes.mjs";
import { collectionOperations } from "../database/index.mjs";
import { createResponse } from "../utils/response-utils.mjs";

/**
 * Parse pagination parameters from query string parameters
 * @param {Object} queryParams - Query string parameters
 * @returns {Object} Pagination parameters
 */
const parsePaginationParams = (queryParams) => {
  const page = queryParams?.page ? parseInt(queryParams.page, 10) : 1;
  const pageSize = queryParams?.pageSize ? parseInt(queryParams.pageSize, 10) : 10;

  // Parse sort parameters
  const sortKey = queryParams?.sortKey || 'executionDate';
  const sortDirection = queryParams?.sortDirection?.toLowerCase() === 'asc' ? 'asc' : 'desc';

  return { page, pageSize, sortKey, sortDirection };
};

/**
 * Parse comma-separated string into array of integers
 * @param {string} value - Comma-separated string of integers
 * @returns {Array<number>} Array of parsed integers
 */
const parseCommaSeparatedIds = (value) => {
  if (!value) return null;
  
  try {
    return value.split(',')
      .map(id => id.trim())
      .filter(id => id.length > 0)
      .map(id => parseInt(id, 10))
      .filter(id => !isNaN(id));
  } catch (error) {
    console.error('Error parsing comma-separated IDs:', error);
    return null;
  }
};

/**
 * Parse filter parameters from query string parameters
 * @param {Object} queryParams - Query string parameters
 * @returns {Object} Filter parameters
 */
const parseFilterParams = (queryParams) => {
  const filters = {};

  // Name filter for collection name
  if (queryParams?.name) {
    filters.name = queryParams.name.trim();
  }

  // Year filter for execution_date
  if (queryParams?.year) {
    filters.year = parseInt(queryParams.year, 10);
  }

  // Institute filter - supports comma-separated values
  if (queryParams?.institute) {
    const instituteIds = parseCommaSeparatedIds(queryParams.institute);
    if (instituteIds && instituteIds.length > 0) {
      filters.instituteId = instituteIds.length === 1 ? instituteIds[0] : instituteIds;
    }
  }

  // Collection filter - supports comma-separated values
  if (queryParams?.collection) {
    const collectionIds = parseCommaSeparatedIds(queryParams.collection);
    if (collectionIds && collectionIds.length > 0) {
      filters.collectionId = collectionIds.length === 1 ? collectionIds[0] : collectionIds;
    }
  }

  // Status filter - supports comma-separated values
  if (queryParams?.status) {
    const statusIds = parseCommaSeparatedIds(queryParams.status);
    if (statusIds && statusIds.length > 0) {
      filters.statusId = statusIds.length === 1 ? statusIds[0] : statusIds;
    }
  }

  // Window filter - supports comma-separated values
  if (queryParams?.window) {
    const windowIds = parseCommaSeparatedIds(queryParams.window);
    if (windowIds && windowIds.length > 0) {
      filters.windowId = windowIds.length === 1 ? windowIds[0] : windowIds;
    }
  }

  // Type filter - supports comma-separated values
  if (queryParams?.type) {
    const typeIds = parseCommaSeparatedIds(queryParams.type);
    if (typeIds && typeIds.length > 0) {
      filters.typeId = typeIds.length === 1 ? typeIds[0] : typeIds;
    }
  }

  // Open date filters
  if (queryParams?.openDateFrom) {
    filters.openDateFrom = new Date(queryParams.openDateFrom);
  }

  if (queryParams?.openDateTo) {
    filters.openDateTo = new Date(queryParams.openDateTo);
  }

  // Close date filters
  if (queryParams?.closeDateFrom) {
    filters.closeDateFrom = new Date(queryParams.closeDateFrom);
  }

  if (queryParams?.closeDateTo) {
    filters.closeDateTo = new Date(queryParams.closeDateTo);
  }

  return filters;
};

/**
 * Handler for GET /collections/history endpoint
 * Retrieves all collection histories with filtering and pagination
 */
export const getAllHistoriesHandler = async (event) => {
  try {
    // Parse pagination and filter parameters
    const { page, pageSize, sortKey, sortDirection } = parsePaginationParams(event.queryStringParameters);
    const filters = parseFilterParams(event.queryStringParameters);

    // Calculate offset
    const offset = (page - 1) * pageSize;

    console.log(`Getting collection histories with offset ${offset}, pageSize ${pageSize}, sortKey ${sortKey}, sortDirection ${sortDirection}, and filters:`, filters);

    // Query database for collection histories with pagination and filters
    const [histories, totalCount] = await Promise.all([
      collectionOperations.collectionHistory.getPaginated(offset, pageSize, filters, sortKey, sortDirection),
      collectionOperations.collectionHistory.getTotalCount(filters)
    ]);

    console.log(`Retrieved ${histories.length} histories out of ${totalCount} total`);

    // Get collection status details for each history entry
    const historiesWithDetails = await Promise.all(
      histories.map(async (history) => {
        // Get collection details
        const collection = await collectionOperations.dataCollection.getById(history.collection_id);

        return {
          ...history,
          collection_name: collection ? collection.name : null,
          description: collection ? collection.description : null,
          collection_type_id: collection ? collection.collection_type_id : null,
          collection_window_id: collection ? collection.collection_window_id : null,
          email_frequency_id: collection ? collection.email_frequency_id : null,
          warning_email: collection ? collection.warning_email : null,
          warning_email_threshold: collection ? collection.warning_email_threshold : null,
          warning_email_message: collection ? collection.warning_email_message : null
        };
      })
    );

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / pageSize);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    // Create response with pagination metadata
    const response = createResponse(STATUSCODE.SUCCESS, {
      histories: historiesWithDetails,
      pagination: {
        page,
        pageSize,
        totalCount,
        totalPages,
        hasNextPage,
        hasPrevPage,
        sortKey,
        sortDirection
      }
    });

    // Log response details
    console.info(
      `Response from: ${event.path} statusCode: ${response.statusCode} numHistories: ${histories.length}`
    );

    return response;
  } catch (err) {
    // Log the error
    console.error("Error retrieving collection histories:", err);

    // Return appropriate error response
    return createResponse(
      err.statusCode || STATUSCODE.SERVER_ERROR,
      { message: err.message || "Error retrieving collection histories", error: err.toString() }
    );
  }
};
