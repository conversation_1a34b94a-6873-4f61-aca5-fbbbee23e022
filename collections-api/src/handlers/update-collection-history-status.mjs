import { collectionOperations } from '../database/index.mjs';
import { createResponse, validateJsonAgainstSchema } from '../utils/response-utils.mjs';
import { STATUSCODE } from '../constants/status-codes.mjs';
import { CollectionStatus } from '../utils/enums.mjs';
import { closeAllInstituteHistories, restoreAllInstituteHistories } from '../utils/collection-institute-history-utils.mjs';

/**
 * Hand<PERSON> for saving a form draft
 * @param {Object} event - The event object from API Gateway
 * @returns {Object} Response object
 */
export const updateCollectionHistoryStatusHandler = async (event) => {
  try {
    // Extract record ID from path parameters
    const recordId = event.pathParameters?.id;
    if (!recordId) {
      return createResponse(STATUSCODE.BAD_REQUEST, { message: 'History ID is required' });
    }

    // Parse request body
    let requestBody;
    try {
      requestBody = JSON.parse(event.body || '{}');
    } catch (parseError) {
      return createResponse(STATUSCODE.BAD_REQUEST, { message: 'Invalid JSON in request body' });
    }

    const { collection_status_id } = requestBody;
    if (collection_status_id === undefined) {
      return createResponse(STATUSCODE.BAD_REQUEST, { message: 'collection_status_id is required in the request body' });
    }

    // Only allow Active or Closed status
    if (!(collection_status_id === CollectionStatus.Active ||
      collection_status_id === CollectionStatus.Closed
    )) {
      return createResponse(STATUSCODE.BAD_REQUEST, { message: 'Only Active or Closed status is allowed for collection history.' });
    }

    // Get the existing collection_history record
    const existingRecord = await collectionOperations.collectionHistory.getById(recordId);
    console.log(existingRecord)
    if (!existingRecord) {
      return createResponse(STATUSCODE.NOT_FOUND, { message: 'Collection history record not found' });
    }

    // Only allow transition: Active -> Closed, or Closed -> Active
    if (
      (collection_status_id === CollectionStatus.Closed && existingRecord.collection_status_id !== CollectionStatus.Active) ||
      (collection_status_id === CollectionStatus.Active && existingRecord.collection_status_id !== CollectionStatus.Closed)
    ) {
      return createResponse(STATUSCODE.BAD_REQUEST, { message: 'Invalid status transition. Only Active collections can be closed, and only Closed collections can be made active.' });
    }

    // Update the record
    const updated = await collectionOperations.collectionHistory.update(recordId, {
      collection_status_id,
      execution_date: new Date().toISOString()
    });

    // If closing the collection, also close all related institute histories
    if (collection_status_id === CollectionStatus.Closed) {
      await closeAllInstituteHistories(recordId);
    }
    // If activating the collection, set all related institute histories to Pending
    if (collection_status_id === CollectionStatus.Active) {
      await restoreAllInstituteHistories(recordId);
    }

    return createResponse(STATUSCODE.SUCCESS, updated);
  } catch (err) {
    return createResponse(STATUSCODE.SERVER_ERROR, { message: 'Internal server error', error: err.message });
  }
}; 