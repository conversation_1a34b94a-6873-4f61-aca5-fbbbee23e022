import { collectionOperations } from '../database/index.mjs';
import { createResponse, validateJsonAgainstSchema } from '../utils/response-utils.mjs';
import { STATUSCODE } from '../constants/status-codes.mjs';
import { CollectionStatus } from '../utils/enums.mjs';
import fs from 'fs/promises';
import fetch from 'node-fetch';
import path from 'path';
import { fillFromExcel } from '../utils/excel-to-form-filler.mjs';

/**
 * <PERSON><PERSON> for saving a form draft
 * @param {Object} event - The event object from API Gateway
 * @returns {Object} Response object
 */
export const saveFormDraftHandler = async (event) => {
  try {
    // Extract record ID from path parameters
    const recordId = event.pathParameters?.id;

    if (!recordId) {
      return createResponse(
        STATUSCODE.BAD_REQUEST,
        { message: 'Record ID is required' }
      );
    }

    // Parse request body
    let requestBody;
    try {
      requestBody = JSON.parse(event.body || '{}');
    } catch (parseError) {
      return createResponse(
        STATUSCODE.BAD_REQUEST,
        { message: 'Invalid JSON in request body' }
      );
    }

    // Extract update fields
    const { form_id, manual_entry } = requestBody;

    // Validate that at least one field is provided
    if (form_id === undefined && manual_entry === undefined) {
      return createResponse(
        STATUSCODE.BAD_REQUEST,
        { message: 'Either form_id or manual_entry must be provided' }
      );
    }

    // Validate form_id if provided
    if (form_id !== undefined && (typeof form_id !== 'number' || form_id <= 0)) {
      return createResponse(
        STATUSCODE.BAD_REQUEST,
        { message: 'form_id must be a positive integer' }
      );
    }

    // Check if the record exists
    const existingRecord = await collectionOperations.collectionInstituteHistory.getById(recordId);
    if (!existingRecord) {
      return createResponse(
        STATUSCODE.NOT_FOUND,
        { message: 'Collection institute history record not found' }
      );
    }

    // Get the template_form_id from the existing record and fetch the form
    const template_form_id = existingRecord.template_form_id;
    let templateForm = null;
    if (template_form_id) {
      templateForm = await collectionOperations.form.getById(template_form_id);
    }

    // Prepare update data
    const updateData = {
      execution_date: new Date(), // Always update execution date
      collection_status_id: CollectionStatus.Draft
    };
    if (form_id !== undefined) updateData.form_id = form_id;

    // If form_id is provided, download the form file from S3 using the presigned URL
    if (form_id !== undefined) {
      // Fetch the form record to get the file name
      const formRecord = await collectionOperations.form.getById(form_id);
      if (!formRecord || !formRecord.file_name) {
        return createResponse(
          STATUSCODE.BAD_REQUEST,
          { message: 'Form file not found for the provided form_id.' }
        );
      }

      // Generate a presigned URL for the form file
      const presignedUrl = await collectionOperations.form.generateFormPresignedUrl(form_id, formRecord.file_name);
      if (!presignedUrl) {
        return createResponse(
          STATUSCODE.SERVER_ERROR,
          { message: 'Failed to generate presigned URL for form file.' }
        );
      }

      console.log(presignedUrl)
      // Download the file from S3
      const response = await fetch(presignedUrl);
      if (!response.ok) {
        return createResponse(
          STATUSCODE.SERVER_ERROR,
          { message: 'Failed to download form file from S3.' }
        );
      }

      // Save the file locally (e.g., to /tmp for AWS Lambda)
      const fileBuffer = await response.buffer();
      const savePath = path.join('/tmp', formRecord.file_name);
      await fs.writeFile(savePath, fileBuffer);
      
      // If templateForm is available and has mappings, fill the form using Excel
      if (templateForm && templateForm.template_form_mappings && templateForm.manual_entry_mappings) {
        try {
          const filledJson = await fillFromExcel(
            templateForm.template_form_mappings,
            templateForm.manual_entry_mappings,
            savePath
          );
          // Save the filledJson as manual_entry
          updateData.manual_entry = filledJson;
        } catch (err) {
          console.error('Error filling form from Excel:', err);
        }
      }
    }

    // Validate and parse manual_entry if provided
    if (manual_entry !== undefined) {
      let manualEntryObj;
      try {
        manualEntryObj = typeof manual_entry === 'string' ? JSON.parse(manual_entry) : manual_entry;
      } catch (e) {
        return createResponse(
          STATUSCODE.BAD_REQUEST,
          { message: 'manual_entry must be a valid JSON string.' }
        );
      }

      // Validate against manual_entry_mappings from templateForm
      if (templateForm && Array.isArray(templateForm.manual_entry_mappings)) {
        let schema = templateForm.manual_entry_mappings;
        if (typeof schema === 'string') {
          try { schema = JSON.parse(schema); } catch {}
        }
        const errors = validateJsonAgainstSchema(manualEntryObj, schema);
        if (errors.length > 0) {
          return createResponse(
            STATUSCODE.BAD_REQUEST,
            { message: 'manual_entry validation failed', errors }
          );
        }
      }
      updateData.manual_entry = manualEntryObj;
    }

    // Update the record
    const updatedRecord = await collectionOperations.collectionInstituteHistory.update(recordId, updateData);

    if (!updatedRecord) {
      return createResponse(
        STATUSCODE.SERVER_ERROR,
        { message: 'Failed to save form draft' }
      );
    }

    // Get the updated record with full details
    const fullRecord = await collectionOperations.collectionInstituteHistory.getById(recordId);

    // Log response details
    console.info(
      `Response from: ${event.path} statusCode: ${STATUSCODE.SUCCESS} recordId: ${recordId}`
    );

    return createResponse(
      STATUSCODE.SUCCESS,
      {
        message: 'Form draft saved successfully',
        record: fullRecord
      }
    );
  } catch (error) {
    console.error('Error in saveFormDraftHandler:', error);
    return createResponse(
      error.statusCode || STATUSCODE.SERVER_ERROR,
      { 
        message: error.message || 'Error saving form draft',
        error: error.toString()
      }
    );
  }
}; 