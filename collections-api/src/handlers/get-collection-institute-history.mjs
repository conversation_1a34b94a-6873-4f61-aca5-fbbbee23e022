import { collectionOperations } from '../database/index.mjs';
import { createResponse } from '../utils/response-utils.mjs';
import { STATUSCODE } from '../constants/status-codes.mjs';
import { processOverallStatus } from '../utils/status-processor.mjs';

/**
 * <PERSON><PERSON> for getting a specific institute's collection history
 * @param {Object} event - The event object from API Gateway
 * @returns {Object} Response object
 */
export const getCollectionInstituteHistoryHandler = async (event) => {
  try {
    // Extract historyId and instituteId from path parameters
    const historyId = event.pathParameters?.id;
    const instituteId = event.pathParameters?.institute;

    if (!historyId) {
      return createResponse(
        STATUSCODE.BAD_REQUEST,
        { message: 'History ID is required' }
      );
    }

    if (!instituteId) {
      return createResponse(
        STATUSCODE.BAD_REQUEST,
        { message: 'Institute ID is required' }
      );
    }

    // Get institute's history for the specified history ID
    const history = await collectionOperations.collectionInstituteHistory.getCollectionHistoryByInstitute(
      historyId,
      instituteId
    );

    if (!history || history.length === 0) {
      return createResponse(
        STATUSCODE.NOT_FOUND,
        { message: 'No history found for the specified history ID and institute' }
      );
    }

    // Get collection details from the first record (they're all the same for a given history)
    const closeDate = history[0]?.close_date || null;
    const now = new Date();
    let days_left = null;
    const statusName = history[0]?.status_name || '';
    if (statusName === 'Submitted') {
      days_left = 0;
    } else if (closeDate) {
      const diff = Math.ceil((closeDate - now) / (1000 * 60 * 60 * 24));
      days_left = diff > 0 ? diff : 0;
    }
    const collectionDetails = {
      collection_id: history[0]?.collection_id || null,
      collection_name: history[0]?.collection_name || '',
      collection_description: history[0]?.collection_description || '',
      open_date: history[0]?.open_date || null,
      close_date: history[0]?.close_date || null,
      collection_type_id: history[0]?.collection_type_id || null,
      collection_window_id: history[0]?.collection_window_id || null,
      days_left: days_left,
      collection_status_id: history[0]?.collection_status_id || null,
      status_namne:  history[0]?.status_name || null,
      year: history[0]?.execution_date ? new Date(history[0].execution_date).getFullYear() : null,
    };

    // Process forms and calculate overall status
    const forms = history.map(record => {
      let manual_entry_with_types = null;
      if (record.manual_entry && record.manual_entry_mappings) {
        let schema;
        if (typeof record.manual_entry_mappings === 'string') {
          try {
            schema = JSON.parse(record.manual_entry_mappings);
          } catch (e) {
            console.error("Failed to parse manual_entry_mappings", e);
            schema = [];
          }
        } else {
          schema = record.manual_entry_mappings;
        }
        // Ensure schema is an array
        if (!Array.isArray(schema)) {
          schema = [];
        }
        const typeMap = schema.reduce((acc, field) => {
          if (field && field.key) {
            acc[field.key] = { type: field.type, label: field.label };
          }
          return acc;
        }, {});

        manual_entry_with_types = Object.entries(record.manual_entry).map(([key, value]) => ({
          key,
          value,
          type: typeMap[key]?.type || 'unknown',
          label: typeMap[key]?.label || key
        }));

      } else if (record.manual_entry) {
        manual_entry_with_types = Object.entries(record.manual_entry).map(([key, value]) => ({
          key,
          value,
          type: 'unknown',
          label: key
        }));
      }

      return {
        id: record.id || null,
        form_id: record.form_id || null,
        template_form_id: record.template_form_id || null,
        form_name: record.form_name || '',
        template_form_name: record.template_form_name || '',
        status_name: record.status_name || '',
        collection_status_id: record.collection_status_id || null,
        download_url: record.download_url || null,
        template_download_url: record.template_download_url || null,
        manual_entry: manual_entry_with_types
      };
    });

    const overallStatus = processOverallStatus(forms);

    // Log response details
    console.info(
      `Response from: ${event.path} statusCode: ${STATUSCODE.SUCCESS} numForms: ${forms.length}`
    );

    return createResponse(
      STATUSCODE.SUCCESS,
      {
        collection: collectionDetails,
        institute: {
          id: history[0]?.institute_id || null,
          name: history[0]?.institute_name || '',
          email: history[0]?.institute_email || '',
          contact: history[0]?.institute_contact || ''
        },
        forms,
        overall_status: overallStatus
      }
    );

  } catch (error) {
    // Log the error
    console.error('Error in getCollectionInstituteHistoryHandler:', error);

    // Return appropriate error response
    return createResponse(
      error.statusCode || STATUSCODE.SERVER_ERROR,
      { 
        message: error.message || 'Error retrieving collection institute history',
        error: error.toString()
      }
    );
  }
}; 