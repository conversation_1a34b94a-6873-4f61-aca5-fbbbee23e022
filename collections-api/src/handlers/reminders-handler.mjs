import { collectionOperations } from '../database/index.mjs';
import { sendEmail } from '/opt/nodejs/index.mjs';
import { processOverallStatus } from '../utils/status-processor.mjs';

/**
 * Calculate the threshold in days based on frequency
 * @param {string} frequency - The frequency name (Daily, Weekly, Bi-Weekly, Custom)
 * @returns {number} Threshold in days
 */
const getFrequencyThreshold = (frequency) => {
  switch (frequency.toLowerCase()) {
    case 'daily':
      return 1;
    case 'weekly':
      return 7;
    case 'bi-weekly':
      return 14;
    case 'custom':
      return 30; // Default for custom frequency
    default:
      return 7; // Default to weekly
  }
};

/**
 * Check if reminder should be sent based on last reminder date and frequency
 * @param {Date} lastReminderDate - The last reminder date
 * @param {number} thresholdDays - The threshold in days
 * @returns {boolean} Whether a reminder should be sent
 */
const shouldSendReminder = (lastReminderDate, thresholdDays) => {
  // Always send reminder if last_reminder is empty
  if (!lastReminderDate) {
    console.log('No previous reminder date found, sending reminder');
    return true;
  }
  
  const now = new Date();
  const lastReminder = new Date(lastReminderDate);
  const diffTime = Math.abs(now - lastReminder);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  const shouldSend = diffDays >= thresholdDays;
  if (shouldSend) {
    console.log(`Threshold met: ${diffDays} days since last reminder (threshold: ${thresholdDays} days)`);
  } else {
    console.log(`Threshold not met: ${diffDays} days since last reminder (threshold: ${thresholdDays} days)`);
  }
  
  return shouldSend;
};

/**
 * Check if warning email should be sent based on close date and threshold
 * @param {Date} closeDate - The collection's close date
 * @param {number} thresholdDays - The warning threshold in days
 * @returns {boolean} Whether a warning email should be sent
 */
const shouldSendWarningEmail = (closeDate, thresholdDays) => {
  if (!closeDate || !thresholdDays) {
    return false;
  }

  const now = new Date();
  const close = new Date(closeDate);
  const diffTime = close - now;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  const shouldSend = diffDays <= thresholdDays && diffDays > 0;
  if (shouldSend) {
    console.log(`Warning threshold met: ${diffDays} days until close date (threshold: ${thresholdDays} days)`);
  } else {
    console.log(`Warning threshold not met: ${diffDays} days until close date (threshold: ${thresholdDays} days)`);
  }

  return shouldSend;
};

/**
 * Handler for sending reminder emails
 * @param {Object} event - The event object from CloudWatch Events
 * @returns {Object} Response object
 */
export const remindersHandler = async (event) => {
  console.log('Event:', JSON.stringify(event, null, 2));

  try {
    // Get current year
    const currentYear = new Date().getFullYear();
    
    // Get active collections for current year with pagination
    const collectionHistories = await collectionOperations.collectionHistory.getPaginated(
      1, // page
      100000, // limit
      { status: 1, year: currentYear } // filters
    );

    console.log(`Found ${collectionHistories.length} active collections for ${currentYear}`);
    console.log(JSON.stringify(collectionHistories));

    const results = {
      processed: 0,
      remindersSent: 0,
      warningsSent: 0,
      errors: 0,
      details: []
    };

    // Process each collection
    for (const collection of collectionHistories) {
      try {
        // Get institutes for this collection
        const filter = {
          collection_id: collection.collection_id,
          collection_history_id: collection.id, // assuming collection.id is the history id
          year: currentYear
        };
        const instituteHistories = await collectionOperations.collectionInstituteHistory.getPaginated(
          0, // offset
          10000, // large limit, or make this configurable
          filter
        );
        const institutesMap = {};

        for (const hist of instituteHistories) {
          if (!institutesMap[hist.institute_id]) {
            institutesMap[hist.institute_id] = {
              id: hist.institute_id,
              name: hist.institute_name,
              email: hist.institute_email,
              forms: []
            };
          }
          institutesMap[hist.institute_id].forms.push({
            id: hist.form_id,
            status_name: hist.status_name,
            collection_status_id: hist.collection_status_id,
            // add other form fields as needed
          });
        }

        // If you need an array:
        const institutes = Object.values(institutesMap);
        if (!institutes || institutes.length === 0) {
          console.log(`No institutes found for collection ${collection.collection_id}`);
          continue;
        }

        // Check for warning email if enabled
        if (collection.reminder_emails && collection.warning_email && collection.warning_email_threshold) {
          if (shouldSendWarningEmail(collection.close_date, collection.warning_email_threshold)) {
            // Send warning email to each institute
            for (const institute of institutes) {
              try {
                // Calculate overall_status for this institute
                const overall_status = processOverallStatus(institute.forms);
                
                if (overall_status === 'Submitted') {
                  continue; // Skip sending email for this institute
                }

                await sendEmail({
                  to: institute.email,
                  subject: `Warning: ${collection.collection_name} Data Collection Closing Soon`,
                  message: collection.warning_email_message || `This collection will close in ${collection.warning_email_threshold} days. Please complete your submission.`,
                  from: process.env.DEFAULT_FROM_EMAIL
                });

                results.warningsSent++;
                console.log(`Sent warning email to institute ${institute.id} for collection ${collection.collection_id}`);
              } catch (emailError) {
                console.error(`Error sending warning email to institute ${institute.id}:`, emailError);
                results.errors++;
              }
            }
          }
        }

        // Skip if reminder emails are not enabled
        if (!collection.reminder_emails) {
          console.log(`Skipping collection ${collection.collection_id}: reminder emails not enabled`);
          continue;
        }

        // Skip if no email frequency is set
        if (!collection.email_frequency_id) {
          console.log(`Skipping collection ${collection.collection_id}: no email frequency set`);
          continue;
        }

        // Get frequency details
        const frequency = await collectionOperations.frequency.getById(collection.email_frequency_id);
        if (!frequency) {
          console.error(`Frequency not found for collection ${collection.collection_id}`);
          continue;
        }

        // Check if reminder should be sent
        const thresholdDays = getFrequencyThreshold(frequency.name);
        if (!shouldSendReminder(collection.last_reminder, thresholdDays)) {
          console.log(`Skipping collection ${collection.collection_id}: threshold not met`);
          continue;
        }

        // Send reminder email to each institute
        for (const institute of institutes) {
          // Calculate overall_status for this institute
          const overall_status = processOverallStatus(institute.forms);

          if (overall_status === 'Submitted') {
            continue; // Skip sending email for this institute
          }

          console.log(JSON.stringify(institute))
          try {
            await sendEmail({
              to: institute.email,
              subject: `Reminder: ${collection.collection_name} Data Collection`,
              message: collection.warning_email_message || 'Please complete your data collection submission.',
              from: process.env.DEFAULT_FROM_EMAIL
            });

            results.remindersSent++;
            console.log(`Sent reminder email to institute ${institute.id} for collection ${collection.collection_id}`);
          } catch (emailError) {
            console.error(`Error sending email to institute ${institute.id}:`, emailError);
            results.errors++;
          }
        }

        // Update last reminder date
        await collectionOperations.dataCollection.update(collection.collection_id, {
          ...collection,
          last_reminder: new Date()
        });

        results.processed++;
        results.details.push({
          collectionId: collection.collection_id,
          collectionName: collection.collection_name,
          institutesCount: institutes.length,
          remindersSent: institutes.length,
          warningsSent: collection.warning_email ? institutes.length : 0
        });

        console.log(`Processed collection ${collection.collection_id}: ${institutes.length} reminders sent`);

      } catch (collectionError) {
        console.error(`Error processing collection ${collection.collection_id}:`, collectionError);
        results.errors++;
      }
    }

    // Log final results
    console.info(
      `Reminders processing completed - Processed: ${results.processed} collections, ` +
      `Sent: ${results.remindersSent} reminders, Warnings: ${results.warningsSent}, Errors: ${results.errors}`
    );
    console.info('Processing details:', JSON.stringify(results.details, null, 2));

  } catch (error) {
    console.error('Error in reminders handler:', error);
  }

  // Always return OK response
  return {
    statusCode: 200,
    body: JSON.stringify({
      message: 'Reminder processing completed',
      timestamp: new Date().toISOString()
    })
  };
}; 