import { collectionOperations } from '../database/index.mjs';
import { createResponse } from '../utils/response-utils.mjs';
import { STATUSCODE } from '../constants/status-codes.mjs';
import { processOverallStatus } from '../utils/status-processor.mjs';

/**
 * <PERSON><PERSON> for getting all institute histories with pagination and filtering
 * @param {Object} event - The event object from API Gateway
 * @returns {Object} Response object
 */
export const getAllInstituteHistoryHandler = async (event) => {
  try {
    // Extract query parameters
    const {
      page = '1',
      pageSize = '10',
      collection: collectionId,
      history: historyId,
      institute: instituteId,
      year,
      status
    } = event.queryStringParameters || {};

    // Validate pagination parameters
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);

    if (isNaN(pageNum) || pageNum < 1) {
      return createResponse(STATUSCODE.BAD_REQUEST, {
        message: 'Invalid page number. Must be a positive integer.'
      });
    }

    if (isNaN(pageSizeNum) || pageSizeNum < 1 || pageSizeNum > 100) {
      return createResponse(STATUSCODE.BAD_REQUEST, {
        message: 'Invalid page size. Must be between 1 and 100.'
      });
    }

    // Build filter object
    const filter = {};
    if (collectionId) filter.collection_id = parseInt(collectionId);
    if (historyId) filter.collection_history_id = parseInt(historyId);
    if (instituteId) filter.institute_id = parseInt(instituteId);
    if (year) filter.year = parseInt(year);
    if (status) filter.collection_status_id = parseInt(status);

    // Calculate offset for pagination
    const offset = (pageNum - 1) * pageSizeNum;

    // Get paginated results
    const results = await collectionOperations.collectionInstituteHistory.getPaginated(
      offset,
      pageSizeNum,
      filter
    );

    if (!results || results.length === 0) {
      return createResponse(STATUSCODE.SUCCESS, {
        data: [],
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          totalRecords: 0,
          totalPages: 0
        }
      });
    }

    // Group results by collection and history
    const groupedResults = results.reduce((acc, item) => {
      if (!item || !item.collection_id || !item.collection_history_id) {
        console.warn('Skipping invalid record:', item);
        return acc;
      }

      const collectionKey = item.collection_id;
      const historyKey = `${item.collection_id}_${item.collection_history_id}`;

      // Initialize collection if not exists
      if (!acc[collectionKey]) {
        acc[collectionKey] = {
          id: item.collection_id,
          name: item.collection_name || '',
          collection_type_id: item.collection_type_id || null,
          collection_window_id: item.collection_window_id || null,
          description: item.collection_description || '',
          histories: []
        };
      }

      // Find or create history
      let history = acc[collectionKey].histories.find(h => h.id === item.collection_history_id);
      if (!history) {
        history = {
          id: item.collection_history_id,
          collection_status_id: item.collection_status_id || null,
          open_date: item.open_date || null,
          close_date: item.close_date || null,
          execution_date: item.execution_date || null,
          status_name: item.status_name || '',
          year: item.execution_date ? new Date(item.execution_date).getFullYear() : null,
          days_left: (() => {
            if (item.status_name === 'Submitted') {
              return 0;
            }
            if (item.close_date) {
              const closeDate = new Date(item.close_date);
              const now = new Date();
              const diff = Math.ceil((closeDate - now) / (1000 * 60 * 60 * 24));
              return diff > 0 ? diff : 0;
            }
            return null;
          })(),
          institutes: {}
        };
        acc[collectionKey].histories.push(history);
      }

      // Group forms by institute
      if (!history.institutes[item.institute_id]) {
        history.institutes[item.institute_id] = {
          id: item.institute_id,
          name: item.institute_name || '',
          email: item.institute_email || '',
          contact: item.institute_contact || '',
          forms: []
        };
      }

      // Add form to institute
      history.institutes[item.institute_id].forms.push({
        id: item.id,
        form_id: item.form_id,
        template_form_id: item.template_form_id,
        form_name: item.form_name || '',
        template_form_name: item.template_form_name || '',
        collection_status_id: item.collection_status_id || null,
        status_name: item.status_name || '',
        download_url: item.download_url || null,
        template_download_url: item.template_download_url || null
      });

      return acc;
    }, {});

    // Calculate overall status for each institute and convert institutes object to array
    const collections = Object.values(groupedResults).map(collection => {
      collection.histories = collection.histories.map(history => {
        // Convert institutes object to array and calculate overall status
        history.institutes = Object.values(history.institutes).map(institute => ({
          ...institute,
          overall_status: processOverallStatus(institute.forms)
        }));
        return history;
      });
      return collection;
    });

    // Get total records for pagination
    const totalRecords = await collectionOperations.collectionInstituteHistory.getTotalCount(filter);
    const totalPages = Math.ceil(totalRecords / pageSizeNum);

    // Log response details
    console.info(
      `Response from: ${event.path} statusCode: ${STATUSCODE.SUCCESS} numResults: ${results.length}`
    );

    return createResponse(STATUSCODE.SUCCESS, {
      collections: collections,
      pagination: {
        page: pageNum,
        pageSize: pageSizeNum,
        totalRecords,
        totalPages
      }
    });

  } catch (error) {
    // Log the error
    console.error('Error in getAllInstituteHistoryHandler:', error);

    // Return appropriate error response
    return createResponse(
      error.statusCode || STATUSCODE.SERVER_ERROR,
      { 
        message: error.message || 'Error retrieving institute histories',
        error: error.toString()
      }
    );
  }
}; 