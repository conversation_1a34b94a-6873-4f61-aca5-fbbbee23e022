import {
  STATUSCODE
} from "../constants/status-codes.mjs";
import { collectionOperations } from "../database/index.mjs";
import { createResponse } from "../utils/response-utils.mjs";

/**
 * Handler for GET /collections/{id}/history endpoint
 * Retrieves history entries for a specific collection
 */
export const getCollectionHistoryHandler = async (event) => {
  console.log('Event:', JSON.stringify(event, null, 2));
  
  try {
    // Get collection ID from path parameters
    const collectionId = event.pathParameters?.id;
    if (!collectionId) {
      return createResponse(STATUSCODE.BAD_REQUEST, {
        message: 'Collection ID is required'
      });
    }

    // Get history ID from query parameters if provided
    const historyId = event.queryStringParameters?.history;

    // Check if collection exists
    const collection = await collectionOperations.dataCollection.getById(collectionId);
    if (!collection) {
      return createResponse(STATUSCODE.NOT_FOUND, {
        message: 'Collection not found'
      });
    }

    // Get collection history
    const history = await collectionOperations.collectionHistory.getByCollectionId(collectionId, historyId);

    // Get all forms and institutes for this collection
    const [forms, institutes] = await Promise.all([
      collectionOperations.dataCollection.getForms(collectionId),
      collectionOperations.dataCollection.getInstitutes(collectionId)
    ]);

    // Get status for each history entry
    const historyWithStatus = await Promise.all(
      history.map(async (entry) => {
        const status = await collectionOperations.collectionStatus.getById(entry.collection_status_id);
        return {
          ...entry,
          status_name: status ? status.name : null
        };
      })
    );

    // Return the response
    return createResponse(STATUSCODE.OK, {
      ...collection,
      forms,
      institutes,
      history: historyWithStatus
    });

  } catch (error) {
    console.error('Error in get collection history handler:', error);
    return createResponse(STATUSCODE.SERVER_ERROR, {
      message: 'Internal server error',
      error: error.message
    });
  }
};
