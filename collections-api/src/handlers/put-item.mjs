import {
  STATUSCODE
} from "../constants/status-codes.mjs";
import { collectionOperations } from "../database/index.mjs";
import { createResponse } from "../utils/response-utils.mjs";
import { CollectionStatus, CollectionWindow } from "../utils/enums.mjs";
import { closeAllInstituteHistories, createCollectionInstituteHistories } from '../utils/collection-institute-history-utils.mjs';


/**
 * Handler for POST /collections endpoint
 * Creates or updates a data collection in the database
 */
export const putItemHandler = async (event) => {
  try {
    // Parse request body
    console.log("Parsing request body...");
    const body = JSON.parse(event.body);
    const {
      id,
      name,
      description,
      open_date,
      close_date,
      collection_type_id,
      collection_window_id,
      email_frequency_id,
      warning_email,
      warning_email_threshold,
      warning_email_message,
      reminder_emails,
      forms,
      institutes,
      history_id
    } = body;

    
    // Prepare collection data
    const collectionData = {
      name,
      description,
      open_date: open_date ? new Date(open_date) : null,
      close_date: close_date ? new Date(close_date) : null,
      collection_type_id: collection_type_id ? parseInt(collection_type_id, 10) : null,
      collection_window_id: collection_window_id ? parseInt(collection_window_id, 10) : null,
      email_frequency_id: email_frequency_id ? parseInt(email_frequency_id, 10) : null,
      warning_email: warning_email === true || warning_email === 'true',
      warning_email_threshold: warning_email_threshold ? parseInt(warning_email_threshold, 10) : null,
      warning_email_message,
      reminder_emails: reminder_emails === true || reminder_emails === 'true'
    };

    let result;
    let isNewCollection = false;

    // Insert or update collection
    if (id) {
      // Update existing collection
      result = await collectionOperations.dataCollection.update(parseInt(id, 10), collectionData);
      
      // If history_id is provided, update the history record's open and close dates
      if (history_id) {
        const historyRecord = await collectionOperations.collectionHistory.getById(parseInt(history_id, 10));
        if (historyRecord) {
          const now = new Date();
          const openDate = collectionData.open_date ? new Date(collectionData.open_date) : null;
          const closeDate = collectionData.close_date ? new Date(collectionData.close_date) : null;
          
          // If Active and openDate > now, error
          if (historyRecord.collection_status_id === CollectionStatus.Active && openDate && openDate > now) {
            return createResponse(STATUSCODE.BAD_REQUEST, { message: 'Open date cannot be in the future for an active collection.' });
          }
          
          // If Active and closeDate is provided and < now, close all institute histories
          if (historyRecord.collection_status_id === CollectionStatus.Active && closeDate && closeDate < now) {
            await updateCollectionHistoryStatusAndDates(
              history_id,
              CollectionStatus.Closed,
              collectionData.open_date,
              collectionData.close_date
            );
            await closeAllInstituteHistories(history_id);
          }
          
          // If Pending and now is within open and close date, activate and create institute histories if not present
          if (
            historyRecord.collection_status_id === CollectionStatus.Pending &&
            openDate && closeDate &&
            now >= openDate &&
            now <= closeDate
          ) {
            // Activate the collection
            await updateCollectionHistoryStatusAndDates(
              history_id,
              CollectionStatus.Active,
              collectionData.open_date,
              collectionData.close_date
            );
            // Check if any institute histories exist for this collection history
            const existingInstituteHistories = await collectionOperations.collectionInstituteHistory.getPaginated(
              0,
              1,
              { collection_history_id: history_id }
            );
            if (!existingInstituteHistories.length) {
              await createCollectionInstituteHistories(historyRecord, historyRecord.collection_id, now);
            }
          }
          
          // If Pending and only open date is provided (adhoc collection) and open date is in the past/present
          if (
            historyRecord.collection_status_id === CollectionStatus.Pending &&
            openDate && !closeDate &&
            now >= openDate
          ) {
            // Activate the adhoc collection
            await updateCollectionHistoryStatusAndDates(
              history_id,
              CollectionStatus.Active,
              collectionData.open_date,
              null
            );
            // Check if any institute histories exist for this collection history
            const existingInstituteHistories = await collectionOperations.collectionInstituteHistory.getPaginated(
              0,
              1,
              { collection_history_id: history_id }
            );
            if (!existingInstituteHistories.length) {
              await createCollectionInstituteHistories(historyRecord, historyRecord.collection_id, now);
            }
          }
        }
      }

      if (!result) {
        return createResponse(STATUSCODE.NOT_FOUND, { message: `Collection with ID ${id} not found` });
      }
    } else {
      // Create new collection
      // Validate required fields
      if (!name) {
        return createResponse(STATUSCODE.BAD_REQUEST, { message: "Name is required for creating a new collection." });
      }
      
      if (!open_date) {
        return createResponse(STATUSCODE.BAD_REQUEST, { message: "Open date is required for creating a new collection." });
      }
      
      if (!collection_window_id) {
        return createResponse(STATUSCODE.BAD_REQUEST, { message: "Collection window ID is required for creating a new collection." });
      }
      
      if (!collection_type_id) {
        return createResponse(STATUSCODE.BAD_REQUEST, { message: "Collection type ID is required for creating a new collection." });
      }
      
      // Validate that recurring collections must have a close date
      if (collectionData.collection_window_id === CollectionWindow.Recurring && !collectionData.close_date) {
        return createResponse(STATUSCODE.BAD_REQUEST, { message: "Close date is required for recurring collections." });
      }

      result = await collectionOperations.dataCollection.create(collectionData);
      isNewCollection = true;
    }

    // Process forms if provided
    if (forms && Array.isArray(forms) && result) {
      // If updating, we need to handle forms differently
      if (!isNewCollection) {
        // Get current forms
        const currentForms = await collectionOperations.dataCollection.getForms(result.id);
        const currentFormIds = currentForms.map(form => form.id);

        // Determine forms to add and remove
        const formsToAdd = forms.filter(formId => !currentFormIds.includes(parseInt(formId, 10)));
        const formsToRemove = currentFormIds.filter(formId => !forms.includes(formId.toString()));

        // Remove forms that are no longer associated
        for (const formId of formsToRemove) {
          await collectionOperations.dataCollection.removeForm(result.id, formId);
        }

        // Add new forms
        for (const formId of formsToAdd) {
          await collectionOperations.dataCollection.addForm(result.id, parseInt(formId, 10));
        }
      } else {
        // For new collections, just add all forms
        for (const formId of forms) {
          await collectionOperations.dataCollection.addForm(result.id, parseInt(formId, 10));
        }
      }
    }

    // Process institutes if provided
    if (institutes && Array.isArray(institutes) && result) {
      // If updating, we need to handle institutes differently
      if (!isNewCollection) {
        // Get current institutes
        const currentInstitutes = await collectionOperations.dataCollection.getInstitutes(result.id);
        const currentInstituteIds = currentInstitutes.map(institute => institute.id);

        // Determine institutes to add and remove
        const institutesToAdd = institutes.filter(instituteId => !currentInstituteIds.includes(parseInt(instituteId, 10)));
        const institutesToRemove = currentInstituteIds.filter(instituteId => !institutes.includes(instituteId.toString()));

        // Remove institutes that are no longer associated
        for (const instituteId of institutesToRemove) {
          await collectionOperations.dataCollection.removeInstitute(result.id, instituteId);
        }

        // Add new institutes
        for (const instituteId of institutesToAdd) {
          await collectionOperations.dataCollection.addInstitute(result.id, parseInt(instituteId, 10));
        }
      } else {
        // For new collections, just add all institutes
        for (const instituteId of institutes) {
          await collectionOperations.dataCollection.addInstitute(result.id, parseInt(instituteId, 10));
        }
      }
    }

    // Add a history entry for new collections based on date ranges
    if (isNewCollection) {
      const now = new Date();
      let statusId;
      let openDate = null;
      let closeDate = null;

      if (collectionData.open_date && collectionData.close_date) {
        // Both dates provided - use existing logic
        openDate = new Date(collectionData.open_date);
        closeDate = new Date(collectionData.close_date);

        console.log(`Comparing dates: Current (${now.toISOString()}), Open (${openDate.toISOString()}), Close (${closeDate.toISOString()})`);

        if (now >= openDate && now <= closeDate) {
          // Current date is between open and close dates
          statusId = CollectionStatus.Active;
          console.log("Current date is within collection period, setting status to 1 (Active)");
        } else if (now < openDate) {
          // Both open and close dates are in the future
          statusId = CollectionStatus.Pending;
          console.log("Collection dates are in the future, setting status to 3 (Pending)");
        } else {
          // Close date is in the past
          statusId = CollectionStatus.Closed;
          console.log("Collection close date is in the past, setting status to 2 (Closed)");
        }
      } else if (collectionData.open_date && !collectionData.close_date) {
        // Only open date provided (adhoc collection without end date)
        openDate = new Date(collectionData.open_date);
        closeDate = null;
        
        console.log(`Adhoc collection: Current (${now.toISOString()}), Open (${openDate.toISOString()}), No close date`);

        if (now >= openDate) {
          // Open date is in the past or present - set as Active
          statusId = CollectionStatus.Active;
          console.log("Open date is in the past/present, setting status to 1 (Active)");
        } else {
          // Open date is in the future - set as Pending
          statusId = CollectionStatus.Pending;
          console.log("Open date is in the future, setting status to 3 (Pending)");
        }
      }

      // Add the history entry with the determined status
      const historyRecord = await collectionOperations.collectionHistory.create({
        collection_id: result.id,
        collection_status_id: statusId,
        open_date: openDate,
        close_date: closeDate,
        execution_date: now
      });

      // --- BEGIN: Add collection institute history records if status is Active ---
      if (statusId === CollectionStatus.Active) {
        // Get all forms and institutes for this collection
        await createCollectionInstituteHistories(historyRecord, historyRecord.collection_id, now);
      }
      // --- END: Add collection institute history records if status is Active ---

      console.log(`Collection history entry created successfully with status_id: ${statusId}`);
    }


    // Get the updated collection with its related data
    const updatedCollection = await collectionOperations.dataCollection.getById(result.id);
    const updatedForms = await collectionOperations.dataCollection.getForms(result.id);
    const updatedInstitutes = await collectionOperations.dataCollection.getInstitutes(result.id);

    // Create successful response
    const response = createResponse(
      STATUSCODE.SUCCESS,
      {
        message: isNewCollection ? "Collection created successfully." : "Collection updated successfully.",
        collection: {
          ...updatedCollection,
          forms: updatedForms,
          institutes: updatedInstitutes
        }
      }
    );

    // Log response details
    console.info(
      `Response from: ${event.path} statusCode: ${response.statusCode} collection: ${name}`
    );

    return response;
  } catch (err) {
    // Log the error
    console.error("Error creating/updating collection:", err);

    // Return appropriate error response
    return createResponse(
      err.statusCode || STATUSCODE.SERVER_ERROR,
      { message: err.message || "Error creating/updating collection", error: err.toString() }
    );
  }
};
// Reusable function to update collection history status and dates
async function updateCollectionHistoryStatusAndDates(history_id, status_id, open_date, close_date) {
  return await collectionOperations.collectionHistory.update(
    parseInt(history_id, 10),
    {
      collection_status_id: status_id,
      open_date,
      close_date,
      execution_date: new Date()
    }
  );
}

