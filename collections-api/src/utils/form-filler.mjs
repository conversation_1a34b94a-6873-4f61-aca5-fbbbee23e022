// Form Filler JavaScript Code
// This code allows you to set values for form items using hierarchical paths

export class FormFiller {
  constructor(formData) {
    this.formData = formData;
  }

  // Set a value using hierarchical path
  setValue(path, value) {
    const pathArray = path.split(':');
    const key = pathArray[pathArray.length - 1]; // Last element is the key
    const sectionPath = pathArray.slice(0, -1); // All elements except the last are the section path
    
    const item = this.findItemByPath(sectionPath, key);
    if (item) {
      item.value = value;
      console.log(`Set ${path} = ${value}`);
      return true;
    } else {
      console.error(`Path not found: ${path}`);
      return false;
    }
  }

  // Get a value using hierarchical path
  getValue(path) {
    const pathArray = path.split(':');
    const key = pathArray[pathArray.length - 1];
    const sectionPath = pathArray.slice(0, -1);
    
    const item = this.findItemByPath(sectionPath, key);
    return item ? item.value : null;
  }

  // Find an item by navigating through the section path and then finding the key
  findItemByPath(sectionPath, key) {
    let current = this.formData;
    
    // Navigate through the section hierarchy
    for (const sectionTitle of sectionPath) {
      current = this.findSection(current, sectionTitle);
      if (!current) {
        return null;
      }
    }
    
    // Find the item with the specified key in the current section
    return this.findItemByKey(current, key);
  }

  // Find a section by title within the current level
  findSection(data, sectionTitle) {
    if (data.items) {
      for (const item of data.items) {
        if (item.sectionTitle === sectionTitle) {
          return item;
        }
      }
    }
    return null;
  }

  // Recursively find an item by key within a section
  findItemByKey(section, key) {
    if (!section.items) return null;
    
    for (const item of section.items) {
      // If this item has the key we're looking for
      if (item.key === key) {
        return item;
      }
      
      // If this item has subsections, search recursively
      if (item.items) {
        const found = this.findItemByKey(item, key);
        if (found) return found;
      }
    }
    
    return null;
  }

  // Batch set multiple values
  setValues(valueMap) {
    const results = {};
    for (const [path, value] of Object.entries(valueMap)) {
      results[path] = this.setValue(path, value);
    }
    return results;
  }

  // Get all paths and their current values (useful for debugging)
  getAllPaths() {
    const paths = [];
    this.collectPaths(this.formData, [], paths);
    return paths;
  }

  // Helper method to collect all possible paths
  collectPaths(data, currentPath, paths) {
    if (data.items) {
      for (const item of data.items) {
        if (item.sectionTitle) {
          // This is a section, recurse with updated path
          this.collectPaths(item, [...currentPath, item.sectionTitle], paths);
        } else if (item.key) {
          // This is a form item, add to paths
          const fullPath = [...currentPath, item.key].join(':');
          paths.push({
            path: fullPath,
            key: item.key,
            label: item.label,
            type: item.type,
            value: item.value
          });
        }
      }
    }
  }
}

// Usage Examples:
// Assuming you have loaded your form.json data into a variable called 'formData'

/*
// Load your form data (in browser or Node.js)
const formData = // ... your form.json data

// Create form filler instance
const filler = new FormFiller(formData);

// Set individual values
filler.setValue('In-State:Undergrad:Men:First-Time Freshmen:firstTimeFreshmen', 100);
filler.setValue('In-State:Undergrad:Men:First-Time Freshmen:firstTimeFreshmenPartTime', 5);
filler.setValue('In-State:Undergrad:Women:First-Time Freshmen:firstTimeFreshmen', 120);

// Set multiple values at once
const valuesToSet = {
  'In-State:Undergrad:Men:First-Time Freshmen:firstTimeFreshmen': 85,
  'In-State:Undergrad:Men:First-Time Freshmen:firstTimeFreshmenPartTime': 0,
  'In-State:Undergrad:Men:First-Time Freshmen:firstTimeFreshmenFTE': 85,
  'In-State:Undergrad:Men:Other Freshmen:otherFreshmen': 40,
  'In-State:Undergrad:Men:Other Freshmen:otherFreshmenPartTime': 3,
  'In-State:Undergrad:Men:Other Freshmen:otherFreshmenFTE': 41.5,
  'Out-of-State:Undergrad:Men:First-Time Freshmen:firstTimeFreshmenOutOfState': 25,
  'Totals:New Admissions:First-time:firstTimeFreshmenFirstTime': 50
};

filler.setValues(valuesToSet);

// Get a value
const value = filler.getValue('In-State:Undergrad:Men:First-Time Freshmen:firstTimeFreshmen');
console.log('Current value:', value);

// Get all possible paths (useful for seeing what paths are available)
const allPaths = filler.getAllPaths();
console.log('All available paths:', allPaths);

// The modified formData can then be saved back to JSON
console.log('Updated form data:', JSON.stringify(formData, null, 2));
*/
