/**
 * Collection Status Enumeration
 * @enum {number}
 */
export const CollectionStatus = {
  /** Active collection */
  Active: 1,
  /** Closed collection */
  Closed: 2,
  /** Pending collection */
  Pending: 3,
  /** Draft collection */
  Draft: 4,
  /** Submitted collection */
  Submitted: 5,
  /** Rejected collection */
  Rejected: 6,
  /** Accepted collection */
  Accepted: 7,
  /** Overdue collection */
  Overdue: 8
};

// Freeze the enum to prevent modifications
Object.freeze(CollectionStatus);

/**
 * Collection Window Enumeration
 * @enum {number}
 */
export const CollectionWindow = {
  /** Adhoc window */
  Adhoc: 1,
  /** Recurring window */
  Recurring: 2
};

Object.freeze(CollectionWindow); 