import { collectionOperations } from '../database/index.mjs';
import { CollectionStatus, CollectionWindow } from '../utils/enums.mjs';

// Helper to close all institute histories for a collection history
export async function closeAllInstituteHistories(collection_history_id) {
  const instituteHistories = await collectionOperations.collectionInstituteHistory.getPaginated(
    0,
    1000,
    { collection_history_id }
  );
  for (const hist of instituteHistories.data || instituteHistories || []) {
    await collectionOperations.collectionInstituteHistory.update(hist.id, {
      prev_collection_status_id: hist.collection_status_id,
      collection_status_id: CollectionStatus.Closed,
      execution_date: new Date().toISOString()
    });
  }
}

// Helper to restore the statuses of a manually closed collection when opened again.
export async function restoreAllInstituteHistories(collection_history_id) {
  const instituteHistories = await collectionOperations.collectionInstituteHistory.getPaginated(
    0,
    1000,
    { collection_history_id }
  );
  for (const hist of instituteHistories.data || instituteHistories || []) {
    await collectionOperations.collectionInstituteHistory.update(hist.id, {
      collection_status_id: hist.prev_collection_status_id || CollectionStatus.Pending,
      prev_collection_status_id: null,
      execution_date: new Date().toISOString()
    });
  }
}

/**
 * Creates a new collection history for the next year with Pending status.
 * @param {Object} collection - The collection object (must have collection_id, open_date, close_date)
 * @returns {Promise<Object>} The created collection history record
 */
export async function createNextYearPendingCollectionHistory(collection) {
  // Do nothing if the collection window is Adhoc
  if (collection.collection_window_id === CollectionWindow.Adhoc) {
    return;
  }

  // Skip if close_date is null or empty (adhoc collections)
  if (!collection.close_date) {
    console.log(`Skipping next year creation for collection ${collection.collection_id} - no close date (adhoc collection)`);
    return;
  }

  const closeDate = new Date(collection.close_date);
  const nextYear = closeDate.getFullYear() + 1;
  const origOpenDate = new Date(collection.open_date);
  const origCloseDate = new Date(collection.close_date);
  const newOpenDate = new Date(nextYear, origOpenDate.getMonth(), origOpenDate.getDate());
  const newCloseDate = new Date(nextYear, origCloseDate.getMonth(), origCloseDate.getDate());
  const executionDateNextYear = new Date();
  executionDateNextYear.setFullYear(executionDateNextYear.getFullYear() + 1);

  return await collectionOperations.collectionHistory.create({
    collection_id: collection.collection_id,
    collection_status_id: CollectionStatus.Pending, // Pending status
    execution_date: executionDateNextYear,
    open_date: newOpenDate,
    close_date: newCloseDate
  });
}

/**
 * Creates collection institute history records for all forms and institutes of a collection
 * @param {Object} historyRecord - The collection history record
 * @param {number} collectionId - The collection ID
 * @param {Date} executionDate - The execution date
 * @returns {Promise<Object>} Object containing forms and institutes counts
 */
export async function createCollectionInstituteHistories(historyRecord, collectionId, executionDate) {
  // Get all forms and institutes for this collection
  const [forms, institutes] = await Promise.all([
    collectionOperations.dataCollection.getForms(collectionId),
    collectionOperations.dataCollection.getInstitutes(collectionId)
  ]);

  // Create collection institute history records for each form and institute
  for (const form of forms) {
    for (const institute of institutes) {
      await collectionOperations.collectionInstituteHistory.create({
        collection_history_id: historyRecord.id,
        institute_id: institute.id,
        form_id: null,
        template_form_id: form.id, // Initially set to same as uploaded form
        collection_status_id: CollectionStatus.Pending, // Pending status
        execution_date: executionDate
      });
    }
  }

  return { forms, institutes };
}

/**
 * Updates results object with collection processing details
 * @param {Object} results - The results object to update
 * @param {Object} collection - The collection object
 * @param {string} oldStatus - The old status
 * @param {string} newStatus - The new status
 * @param {Date} openDate - The open date
 * @param {Date|null} closeDate - The close date
 * @param {Date} currentDate - The current date
 * @param {string} action - The action performed ('created' or 'updated')
 * @param {Array} forms - The forms array
 * @param {Array} institutes - The institutes array
 */
export function updateResults(results, collection, oldStatus, newStatus, openDate, closeDate, currentDate, action, forms = [], institutes = []) {
  const resultDetail = {
    collectionId: collection.collection_id,
    collectionName: collection.collection_name,
    oldStatus: oldStatus,
    newStatus: newStatus,
    openDate: openDate,
    closeDate: closeDate,
    currentDate: currentDate.toISOString(),
    action: action
  };

  if (forms.length > 0 || institutes.length > 0) {
    resultDetail.formsCount = forms.length;
    resultDetail.institutesCount = institutes.length;
  }

  results.details.push(resultDetail);

  if (action === 'created') {
    results.created++;
  } else if (action === 'updated') {
    results.updated++;
  }
} 