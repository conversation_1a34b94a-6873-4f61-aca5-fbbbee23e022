// Excel to Form Filler - Reads Excel data and fills form.json using mapping JSON
import xlsx from 'xlsx';
import { FormFiller } from './form-filler.mjs';

export class ExcelToFormFiller {
  /**
   * @param {Array} mapping - Array of mapping objects (not a file path)
   * @param {Object} formData - The form structure (not a file path)
   * @param {string} excelPath - Path to the Excel file
   */
  constructor(mapping, formData, excelPath = 'A1.1 Workbook.xlsx') {
    this.mapping = mapping;
    this.formData = formData;
    this.excelPath = excelPath;
    this.filler = new FormFiller(this.formData);

    // Load Excel workbook
    this.workbook = xlsx.readFile(excelPath);
    this.worksheet = null;

    console.log(`Loaded ${this.mapping.length} mappings (direct JSON)`);
    console.log(`Loaded form data (direct JSON)`);
    console.log(`Loaded Excel workbook: ${excelPath}`);
    console.log(`Available worksheets: ${this.workbook.SheetNames.join(', ')}`);
  }

  // Set the active worksheet
  setWorksheet(sheetName) {
    if (this.workbook.SheetNames.includes(sheetName)) {
      this.worksheet = this.workbook.Sheets[sheetName];
      console.log(`Active worksheet set to: ${sheetName}`);
      return true;
    } else {
      console.error(`Worksheet '${sheetName}' not found. Available: ${this.workbook.SheetNames.join(', ')}`);
      return false;
    }
  }

  // Get value from Excel cell
  getCellValue(cellAddress) {
    if (!this.worksheet) {
      throw new Error('No worksheet selected. Call setWorksheet() first.');
    }

    const cell = this.worksheet[cellAddress];
    if (!cell) {
      return null; // Cell is empty or doesn't exist
    }

    // Return the cell value, handling different types
    return cell.v; // .v contains the actual value
  }

  // Convert value based on type
  convertValue(value, type) {
    if (value === null || value === undefined || value === '') {
      return type === 'number' ? 0 : '';
    }

    switch (type) {
      case 'number':
        const num = parseFloat(value);
        return isNaN(num) ? 0 : num;
      case 'text':
      case 'tel':
        return String(value);
      default:
        return value;
    }
  }

  // Fill form with Excel data
  fillFormFromExcel(sheetName = null) {
    // Set worksheet if provided
    if (sheetName && !this.setWorksheet(sheetName)) {
      return { success: false, error: `Could not set worksheet: ${sheetName}` };
    }

    if (!this.worksheet) {
      // Try to use the first worksheet if none is set
      if (this.workbook.SheetNames.length > 0) {
        this.setWorksheet(this.workbook.SheetNames[0]);
      } else {
        return { success: false, error: 'No worksheets available' };
      }
    }

    let successCount = 0;
    let errorCount = 0;
    const errors = [];
    const updates = [];

    // Process each mapping
    this.mapping.forEach(mapping => {
      try {
        // Get value from Excel
        const rawValue = this.getCellValue(mapping.cell);
        
        // Convert value based on type
        const convertedValue = this.convertValue(rawValue, mapping.type);
        
        // Set value in form
        const success = this.filler.setValue(mapping.key, convertedValue);
        
        if (success) {
          successCount++;
          updates.push({
            key: mapping.key,
            cell: mapping.cell,
            value: convertedValue,
            type: mapping.type,
            rawValue: rawValue
          });
        } else {
          errorCount++;
          errors.push(`Failed to set ${mapping.key} from ${mapping.cell}`);
        }
      } catch (error) {
        errorCount++;
        errors.push(`Error processing ${mapping.key} from ${mapping.cell}: ${error.message}`);
      }
    });

    return {
      success: true,
      successCount,
      errorCount,
      errors,
      updates: updates.slice(0, 10), // Show first 10 updates as sample
      totalUpdates: updates.length
    };
  }

  // Return the updated form data as JSON instead of saving to a file
  getUpdatedForm() {
    return this.formData;
  }

  // Get summary of Excel data for specific cells
  getExcelSummary(sampleCells = ['B7', 'C7', 'D7', 'E7', 'F7']) {
    if (!this.worksheet) {
      return { error: 'No worksheet selected' };
    }

    const summary = {
      worksheet: this.worksheet,
      sampleData: {}
    };

    sampleCells.forEach(cell => {
      const value = this.getCellValue(cell);
      summary.sampleData[cell] = value;
    });

    return summary;
  }

  // Validate mappings against Excel sheet
  validateMappings() {
    if (!this.worksheet) {
      return { error: 'No worksheet selected' };
    }

    const validation = {
      totalMappings: this.mapping.length,
      emptyCells: 0,
      populatedCells: 0,
      invalidCells: 0,
      cellSamples: []
    };

    this.mapping.slice(0, 20).forEach(mapping => { // Check first 20 for sample
      const value = this.getCellValue(mapping.cell);
      const sample = {
        key: mapping.key,
        cell: mapping.cell,
        type: mapping.type,
        value: value,
        status: value === null ? 'empty' : 'populated'
      };

      validation.cellSamples.push(sample);
      
      if (value === null) {
        validation.emptyCells++;
      } else {
        validation.populatedCells++;
      }
    });

    return validation;
  }
}

/**
 * Fill a form structure using Excel data and a mapping.
 * @param {Array} mapping - Array of mapping objects
 * @param {Object} formData - The form structure
 * @param {string} excelPath - Path to the Excel file
 * @param {string} [sheetName] - Optional worksheet name
 * @returns {Object} - The filled form JSON
 */
export async function fillFromExcel(mapping, formData, excelPath, sheetName = null) {
  const filler = new ExcelToFormFiller(mapping, formData, excelPath);
  if (sheetName) {
    filler.setWorksheet(sheetName);
  }
  filler.fillFormFromExcel();
  return filler.getUpdatedForm();
}
