/**
 * Process the overall status based on form statuses
 * @param {Array} forms - Array of form history records
 * @param {string} closingDate - Closing date of the collection
 * @returns {string} Overall status
 */
export const processOverallStatus = (forms) => {
  if (!forms || forms.length === 0) {
    return 'Pending';
  }

  // Check if all forms are closed
  const allClosed = forms.every(form => 
  form.status_name.toLowerCase() === 'closed'
  );
  if (allClosed) {
    return 'Closed';
  }

  // Check if any form is rejected
  const hasRejected = forms.some(form => form.status_name.toLowerCase() === 'rejected');
  if (hasRejected) {
    return 'Rejected';
  }

  // Check if any form is pending or draft
  const hasPending = forms.some(form => 
    ['pending', 'draft'].includes(form.status_name.toLowerCase())
  );
  if (hasPending) {
    return 'Pending';
  }

  // Check if all forms are accepted
  const allAccepted = forms.every(form => 
    form.status_name.toLowerCase() === 'accepted'
  );
  if (allAccepted) {
    return 'Accepted';
  }

  // If all forms are submitted but not all accepted
  const allSubmitted = forms.every(form => 
    form.status_name.toLowerCase() === 'submitted'
  );
  if (allSubmitted) {
    return 'Submitted';
  }

  const hasOverdue = forms.some(form => 
    ['overdue'].includes(form.status_name.toLowerCase())
  );
  if (hasOverdue) {
    return 'Overdue';
  }

  return 'Pending';
}; 