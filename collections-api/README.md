# Collections API

This project contains source code and supporting files for a serverless application that you can deploy with the AWS Serverless Application Model (AWS SAM) command line interface (CLI). It includes the following files and folders:

- `src` - Code for the application's Lambda function.
- `events` - Invocation events that you can use to invoke the function.
- `__tests__` - Unit tests for the application code.
- `template.yaml` - A template that defines the application's AWS resources.

## API Documentation

The Collections API provides endpoints for managing data collections, including creating, retrieving, and filtering collections. It also provides endpoints for retrieving reference data such as collection statuses, types, windows, frequencies, and forms.

### Base URL

For local development:
```
http://localhost:3000
```

For production:
```
https://{api-id}.execute-api.{region}.amazonaws.com/Prod
```

### Endpoints

#### Collections

##### Get All Collections

Retrieves a paginated list of collections with optional filtering.

**URL**: `/collections`

**Method**: `GET`

**Query Parameters**:

**Pagination Parameters**:
- `page` (optional): Page number (default: 1)
- `pageSize` (optional): Number of items per page (default: 10, max: 100)

**Filter Parameters**:
- `type` (optional): Filter by collection type ID
  - Example: `type=1` will return collections of type ID 1
- `status` (optional): Filter by collection status ID
  - Available values:
    - `1`: Active
    - `2`: Closed
    - `3`: Pending
  - Example: `status=1` will return collections with Active status
- `window` (optional): Filter by collection window ID
  - Example: `window=2` will return collections with window ID 2

**Date Range Filters**:
- `openDateFrom` (optional): Filter by open date (from) in ISO format
  - Example: `openDateFrom=2023-01-01T00:00:00Z`
- `openDateTo` (optional): Filter by open date (to) in ISO format
  - Example: `openDateTo=2023-12-31T23:59:59Z`
- `closeDateFrom` (optional): Filter by close date (from) in ISO format
  - Example: `closeDateFrom=2023-06-01T00:00:00Z`
- `closeDateTo` (optional): Filter by close date (to) in ISO format
  - Example: `closeDateTo=2023-06-30T23:59:59Z`

**Example Request**:
```bash
curl "http://localhost:3000/collections?page=1&pageSize=5&type=1&openDateFrom=2023-01-01"
```

**Example Response**:
```json
{
  "collections": [
    {
      "id": 1,
      "name": "Spring 2023 Data Collection",
      "description": "Collection of data for Spring 2023 semester",
      "open_date": "2023-01-15T00:00:00.000Z",
      "close_date": "2023-05-15T00:00:00.000Z",
      "collection_type_id": 1,
      "collection_window_id": 1,
      "email_frequency_id": 1,
      "warning_email": true,
      "warning_email_threshold": 7,
      "warning_email_message": "Please submit your data within 7 days."
    },
    {
      "id": 2,
      "name": "Fall 2023 Data Collection",
      "description": "Collection of data for Fall 2023 semester",
      "open_date": "2023-08-15T00:00:00.000Z",
      "close_date": "2023-12-15T00:00:00.000Z",
      "collection_type_id": 1,
      "collection_window_id": 2,
      "email_frequency_id": 1,
      "warning_email": true,
      "warning_email_threshold": 7,
      "warning_email_message": "Please submit your data within 7 days."
    }
  ],
  "pagination": {
    "page": 1,
    "pageSize": 5,
    "totalCount": 2,
    "totalPages": 1,
    "hasNextPage": false,
    "hasPreviousPage": false
  },
  "filters": {
    "typeId": 1,
    "openDateFrom": "2023-01-01T00:00:00.000Z"
  }
}
```

##### Get Collection by ID

Retrieves a specific collection by ID, including its related forms and institutes.

**URL**: `/collections/{id}`

**Method**: `GET`

**URL Parameters**:

- `id`: The ID of the collection to retrieve

**Example Request**:
```bash
curl "http://localhost:3000/collections/1"
```

**Example Response**:
```json
{
  "id": 1,
  "name": "Spring 2023 Data Collection",
  "description": "Collection of data for Spring 2023 semester",
  "open_date": "2023-01-15T00:00:00.000Z",
  "close_date": "2023-05-15T00:00:00.000Z",
  "collection_type_id": 1,
  "collection_window_id": 1,
  "email_frequency_id": 1,
  "warning_email": true,
  "warning_email_threshold": 7,
  "warning_email_message": "Please submit your data within 7 days.",
  "forms": [
    {
      "id": 1,
      "name": "Student Enrollment Form",
      "description": "Form for collecting student enrollment data"
    },
    {
      "id": 2,
      "name": "Faculty Information Form",
      "description": "Form for collecting faculty information"
    }
  ],
  "institutes": [
    {
      "id": 1,
      "name": "University A"
    },
    {
      "id": 2,
      "name": "College B"
    },
    {
      "id": 3,
      "name": "Institute C"
    }
  ]
}
```

##### Create or Update Collection

Creates a new collection or updates an existing one.

**URL**: `/collections`

**Method**: `POST`

**Request Body**:

- `id` (optional): The ID of the collection to update (if not provided, a new collection will be created)
- `name` (required): The name of the collection
- `description` (optional): The description of the collection
- `open_date` (optional): The open date of the collection in ISO format
- `close_date` (optional): The close date of the collection in ISO format

- `collection_type_id` (optional): The ID of the collection type
- `collection_window_id` (optional): The ID of the collection window
- `email_frequency_id` (optional): The ID of the email frequency
- `warning_email` (optional): Whether to send warning emails
- `warning_email_threshold` (optional): The threshold for sending warning emails
- `warning_email_message` (optional): The message to include in warning emails
- `reminder_emails` (optional): Whether to send reminder emails
- `forms` (optional): An array of form IDs to associate with the collection
- `institutes` (optional): An array of institute IDs to associate with the collection

**Example Request**:
```bash
curl -X POST "http://localhost:3000/collections" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Spring 2024 Data Collection",
    "description": "Collection of data for Spring 2024 semester",
    "open_date": "2024-01-15T00:00:00.000Z",
    "close_date": "2024-05-15T00:00:00.000Z",
    "collection_type_id": 1,
    "collection_window_id": 1,
    "email_frequency_id": 1,
    "warning_email": true,
    "warning_email_threshold": 7,
    "warning_email_message": "Please submit your data within 7 days.",
    "reminder_emails": true,
    "forms": ["1", "2"],
    "institutes": ["1", "2", "3"]
  }'
```

**Example Response**:
```json
{
  "message": "Collection created successfully.",
  "collection": {
    "id": 3,
    "name": "Spring 2024 Data Collection",
    "description": "Collection of data for Spring 2024 semester",
    "open_date": "2024-01-15T00:00:00.000Z",
    "close_date": "2024-05-15T00:00:00.000Z",
    "collection_type_id": 1,
    "collection_window_id": 1,
    "email_frequency_id": 1,
    "warning_email": true,
    "warning_email_threshold": 7,
    "warning_email_message": "Please submit your data within 7 days.",
    "reminder_emails": true,
    "forms": [
      {
        "id": 1,
        "name": "Student Enrollment Form",
        "description": "Form for collecting student enrollment data"
      },
      {
        "id": 2,
        "name": "Faculty Information Form",
        "description": "Form for collecting faculty information"
      }
    ],
    "institutes": [
      {
        "id": 1,
        "name": "University A"
      },
      {
        "id": 2,
        "name": "College B"
      },
      {
        "id": 3,
        "name": "Institute C"
      }
    ]
  }
}
```

##### Get Collection History

Retrieves the history of status changes for a specific collection.

**URL**: `/collections/{id}/history`

**Method**: `GET`

**URL Parameters**:

- `id`: The ID of the collection to retrieve history for

**Example Request**:
```bash
curl "http://localhost:3000/collections/1/history"
```

**Example Response**:
```json
{
  "id": 1,
  "name": "Spring 2023 Data Collection",
  "description": "Collection of data for Spring 2023 semester",
  "open_date": "2023-01-15T00:00:00.000Z",
  "close_date": "2023-05-15T00:00:00.000Z",
  "collection_type_id": 1,
  "collection_window_id": 1,
  "email_frequency_id": 1,
  "warning_email": true,
  "warning_email_threshold": 7,
  "warning_email_message": "Please submit your data within 7 days.",
  "forms": [
    {
      "id": 1,
      "name": "Student Enrollment Form",
      "description": "Form for collecting student enrollment data"
    }
  ],
  "institutes": [
    {
      "id": 1,
      "name": "University A"
    }
  ],
  "history": [
    {
      "id": 1,
      "collection_id": 1,
      "collection_status_id": 1,
      "execution_date": "2023-01-15T00:00:00.000Z",
      "status_name": "Active"
    }
  ]
}
```

##### Get All Collection Histories

Retrieves all collection histories with filtering, pagination, and sorting.

**URL**: `/collections/history`

**Method**: `GET`

**Query Parameters**:

**Pagination Parameters**:
- `page` (optional): Page number (default: 1)
- `pageSize` (optional): Number of items per page (default: 10, max: 100)

**Sorting Parameters**:
- `sortKey` (optional): Field to sort by (default: 'executionDate')
  - Available sort keys:
    - `executionDate`: Sort by the date when the history entry was created
    - `collection`: Sort by collection ID
    - `status`: Sort by status ID
    - `open_date`: Sort by the collection's open date
    - `close_date`: Sort by the collection's close date
- `sortDirection` (optional): Sort direction (default: 'desc')
  - Available values: 'asc' (ascending), 'desc' (descending)

**Filter Parameters**:
- `year` (optional): Filter by year of execution date
  - Example: `year=2023` will return history entries from 2023
- `collection` (optional): Filter by collection ID
  - Example: `collection=5` will return history entries for collection with ID 5
- `status` (optional): Filter by status ID
  - Available values:
    - `1`: Active
    - `2`: Closed
    - `3`: Pending
  - Example: `status=1` will return history entries with Active status
- `institute` (optional): Filter by institute ID
  - Example: `institute=3` will return history entries for collections associated with institute ID 3
- `type` (optional): Filter by collection type ID
  - Example: `type=2` will return history entries for collections of type ID 2
- `window` (optional): Filter by collection window ID
  - Example: `window=1` will return history entries for collections with window ID 1

**Date Range Filters**:
- `openDateFrom` (optional): Filter by open date (from) in ISO format
  - Example: `openDateFrom=2023-01-01T00:00:00Z`
- `openDateTo` (optional): Filter by open date (to) in ISO format
  - Example: `openDateTo=2023-12-31T23:59:59Z`
- `closeDateFrom` (optional): Filter by close date (from) in ISO format
  - Example: `closeDateFrom=2023-06-01T00:00:00Z`
- `closeDateTo` (optional): Filter by close date (to) in ISO format
  - Example: `closeDateTo=2023-06-30T23:59:59Z`

**Example Request**:
```bash
curl "http://localhost:3000/collections/history?page=1&pageSize=5&status=1&year=2023&sortKey=executionDate&sortDirection=desc"
```

**Example Response**:
```json
{
  "histories": [
    {
      "id": 2,
      "collection_id": 1,
      "collection_status_id": 1,
      "execution_date": "2023-01-15T00:00:00.000Z",
      "status_name": "Active",
      "collection_name": "Spring 2023 Data Collection",
      "collection_type_id": 1,
      "collection_window_id": 1
    },
    {
      "id": 4,
      "collection_id": 2,
      "collection_status_id": 1,
      "execution_date": "2023-08-15T00:00:00.000Z",
      "status_name": "Active",
      "collection_name": "Fall 2023 Data Collection",
      "collection_type_id": 1,
      "collection_window_id": 2
    }
  ],
  "pagination": {
    "page": 1,
    "pageSize": 5,
    "totalCount": 2,
    "totalPages": 1,
    "hasNextPage": false,
    "hasPrevPage": false,
    "sortKey": "executionDate",
    "sortDirection": "desc"
  }
}
```

#### Reference Data

##### Get All Forms

Retrieves all forms from the database.

**URL**: `/collections/forms`

**Method**: `GET`

**Example Request**:
```bash
curl "http://localhost:3000/collections/forms"
```

**Example Response**:
```json
[
  {
    "id": 1,
    "name": "Student Enrollment Form",
    "description": "Form for collecting student enrollment data",
    "is_template": true,
    "file_name": "student_enrollment.pdf",
    "download_url": "https://forms-bucket.s3.amazonaws.com/1/student_enrollment.pdf?X-Amz-Algorithm=..."
  },
  {
    "id": 2,
    "name": "Faculty Information Form",
    "description": "Form for collecting faculty information",
    "is_template": true,
    "file_name": "faculty_info.pdf",
    "download_url": "https://forms-bucket.s3.amazonaws.com/2/faculty_info.pdf?X-Amz-Algorithm=..."
  },
  {
    "id": 3,
    "name": "Financial Aid Form",
    "description": "Form for collecting financial aid data",
    "is_template": false,
    "file_name": "financial_aid.pdf"
  }
]
```

##### Get All Collection Statuses

Retrieves all collection statuses from the database.

**URL**: `/collections/statuses`

**Method**: `GET`

**Example Request**:
```bash
curl "http://localhost:3000/collections/statuses"
```

**Example Response**:
```json
[
  {
    "id": 1,
    "name": "Open",
    "description": "Collection is open for submissions"
  },
  {
    "id": 2,
    "name": "Closed",
    "description": "Collection is closed for submissions"
  },
  {
    "id": 3,
    "name": "Draft",
    "description": "Collection is in draft mode"
  }
]
```

##### Get All Collection Types

Retrieves all collection types from the database.

**URL**: `/collections/types`

**Method**: `GET`

**Example Request**:
```bash
curl "http://localhost:3000/collections/types"
```

**Example Response**:
```json
[
  {
    "id": 1,
    "name": "Survey",
    "description": "A survey collection type"
  },
  {
    "id": 2,
    "name": "Report",
    "description": "A report collection type"
  },
  {
    "id": 3,
    "name": "Data Request",
    "description": "A data request collection type"
  }
]
```

##### Get All Collection Windows

Retrieves all collection windows from the database.

**URL**: `/collections/windows`

**Method**: `GET`

**Example Request**:
```bash
curl "http://localhost:3000/collections/windows"
```

**Example Response**:
```json
[
  {
    "id": 1,
    "name": "Spring",
    "description": "Spring semester window"
  },
  {
    "id": 2,
    "name": "Fall",
    "description": "Fall semester window"
  },
  {
    "id": 3,
    "name": "Summer",
    "description": "Summer semester window"
  }
]
```

##### Get All Collection Frequencies

Retrieves all collection frequencies from the database.

**URL**: `/collections/frequencies`

**Method**: `GET`

**Example Request**:
```bash
curl "http://localhost:3000/collections/frequencies"
```

**Example Response**:
```json
[
  {
    "id": 1,
    "name": "Annual",
    "description": "Once per year"
  },
  {
    "id": 2,
    "name": "Biannual",
    "description": "Twice per year"
  },
  {
    "id": 3,
    "name": "Quarterly",
    "description": "Four times per year"
  }
]
```

## Forms Management

### Get All Template Forms
- **Endpoint**: `GET /collections/forms`
- **Description**: Retrieves all template forms from the database
- **Response**: Array of form objects with the following structure:
  ```json
  {
    "id": number,
    "name": string,
    "description": string | null,
    "is_template": boolean,
    "file_name": string | null,
    "download_url": string | null  // Only present if is_template is true and file_name exists
  }
  ```

### Get Form by ID
- **Endpoint**: `GET /collections/forms/{id}`
- **Description**: Retrieves a specific form by ID
- **Response**: Form object with the same structure as above

### Upload Forms
- **Endpoint**: `POST /collections/forms`
- **Description**: Creates new forms or updates existing forms and returns presigned URLs for file uploads
- **Request Body**: Array of form objects
  ```json
  [
    {
      "id": number,            // Optional - if provided, updates existing form by ID
      "name": string,          // Required
      "description": string,   // Optional
      "is_template": boolean,  // Optional, defaults to false
      "file_name": string     // Required
    }
  ]
  ```
- **Response**: Array of results for each form with proper HTTP status codes
  ```json
  {
    "message": "Forms processed successfully",
    "results": [
      {
        "statusCode": number,  // HTTP status code (200, 400, 404, 409, 500)
        "body": {
          "success": boolean,
          "form": {
            "id": number,
            "name": string,
            "description": string | null,
            "is_template": boolean,
            "file_name": string
          },
          "upload_url": string  // Presigned URL for file upload
        }
      }
    ]
  }
  ```

### Form Processing Logic

The upload forms endpoint now supports two modes of operation:

1. **Update by ID**: If an `id` is provided in the request body, the system will:
   - Look up the form by the provided ID
   - Update the existing form with the new data
   - Return a 404 error if the form ID doesn't exist

2. **Create New**: If no `id` is provided, the system will:
   - Check if a form with the same name already exists
   - Return a 409 Conflict error if a duplicate name is found
   - Create a new form if the name is unique

### Database Layer Enhancements

A new database function has been added to improve form name validation:

- **`getByName(name)`**: Efficiently searches for forms by name using a direct database query
  - Returns the form object if found, or `null` if not found
  - Includes presigned download URL generation for template forms
  - Replaces the previous inefficient `getAll()` + `find()` approach

### Form File Storage
- Forms are stored in S3 with the path structure: `{form_id}/{file_name}`
- Presigned URLs are generated for both uploading and downloading form files
- Upload URLs expire after 1 hour
- Download URLs are generated on-demand when retrieving forms

### Form Properties
- `is_template`: Boolean flag indicating if the form is a template
  - Template forms are used as base forms for collections
  - Only template forms are returned by the GET /collections/forms endpoint
- `file_name`: Name of the file associated with the form
  - Required when creating/updating a form
  - Used to generate the S3 key for file storage
- `download_url`: Temporary URL for downloading the form file
  - Only generated for template forms with a file_name
  - Expires after 1 hour
- `upload_url`: Temporary URL for uploading the form file
  - Generated when creating/updating a form
  - Expires after 1 hour

### Error Handling

The API now returns proper HTTP status codes for different scenarios:

- **400 Bad Request**: Missing required fields (`name` or `file_name`) or invalid request format
- **404 Not Found**: Form with the specified ID doesn't exist (when updating by ID)
- **409 Conflict**: Form with the same name already exists (when creating new forms)
- **500 Server Error**: Internal server error during form processing

### Example Requests

**Create a new form:**
```bash
curl -X POST "http://localhost:3000/collections/forms" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "name": "Student Enrollment Form 2024",
      "description": "Updated form for student enrollment data",
      "is_template": true,
      "file_name": "student_enrollment_2024.pdf"
    }
  ]'
```

**Update an existing form by ID:**
```bash
curl -X POST "http://localhost:3000/collections/forms" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "id": 1,
      "name": "Updated Student Enrollment Form",
      "description": "Updated description",
      "is_template": true,
      "file_name": "updated_student_enrollment.pdf"
    }
  ]'
```

**Example Success Response:**
```json
{
  "message": "Forms processed successfully",
  "results": [
    {
      "statusCode": 200,
      "body": {
        "success": true,
        "form": {
          "id": 1,
          "name": "Updated Student Enrollment Form",
          "description": "Updated description",
          "is_template": true,
          "file_name": "updated_student_enrollment.pdf"
        },
        "upload_url": "https://forms-bucket.s3.amazonaws.com/1/updated_student_enrollment.pdf?X-Amz-Algorithm=..."
      }
    }
  ]
}
```

**Example Error Response (Duplicate Name):**
```json
{
  "message": "Forms processed successfully",
  "results": [
    {
      "statusCode": 409,
      "body": {
        "success": false,
        "error": "Form with name 'Student Enrollment Form 2024' already exists"
      }
    }
  ]
}
```

### CORS Support
All endpoints support CORS with the following configuration:
- Allowed Methods: GET, POST, OPTIONS
- Allowed Headers: Content-Type, X-Amz-Date, Authorization, X-Api-Key, X-Amz-Security-Token
- Allowed Origin: *

## Other Endpoints
[Previous documentation for other endpoints remains unchanged...]

The application uses several AWS resources, including Lambda functions, an API Gateway API. These resources are defined in the `template.yaml` file in this project. You can update the template to add AWS resources through the same deployment process that updates your application code.

If you prefer to use an integrated development environment (IDE) to build and test your application, you can use the AWS Toolkit.
The AWS Toolkit is an open-source plugin for popular IDEs that uses the AWS SAM CLI to build and deploy serverless applications on AWS. The AWS Toolkit also adds step-through debugging for Lambda function code.

To get started, see the following:

* [CLion](https://docs.aws.amazon.com/toolkit-for-jetbrains/latest/userguide/welcome.html)
* [GoLand](https://docs.aws.amazon.com/toolkit-for-jetbrains/latest/userguide/welcome.html)
* [IntelliJ](https://docs.aws.amazon.com/toolkit-for-jetbrains/latest/userguide/welcome.html)
* [WebStorm](https://docs.aws.amazon.com/toolkit-for-jetbrains/latest/userguide/welcome.html)
* [Rider](https://docs.aws.amazon.com/toolkit-for-jetbrains/latest/userguide/welcome.html)
* [PhpStorm](https://docs.aws.amazon.com/toolkit-for-jetbrains/latest/userguide/welcome.html)
* [PyCharm](https://docs.aws.amazon.com/toolkit-for-jetbrains/latest/userguide/welcome.html)
* [RubyMine](https://docs.aws.amazon.com/toolkit-for-jetbrains/latest/userguide/welcome.html)
* [DataGrip](https://docs.aws.amazon.com/toolkit-for-jetbrains/latest/userguide/welcome.html)
* [VS Code](https://docs.aws.amazon.com/toolkit-for-vscode/latest/userguide/welcome.html)
* [Visual Studio](https://docs.aws.amazon.com/toolkit-for-visual-studio/latest/user-guide/welcome.html)

## Deploy the sample application

The AWS SAM CLI is an extension of the AWS CLI that adds functionality for building and testing Lambda applications. It uses Docker to run your functions in an Amazon Linux environment that matches Lambda. It can also emulate your application's build environment and API.

To use the AWS SAM CLI, you need the following tools:

* AWS SAM CLI - [Install the AWS SAM CLI](https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/serverless-sam-cli-install.html).
* Node.js - [Install Node.js 20](https://nodejs.org/en/), including the npm package management tool.
* Docker - [Install Docker community edition](https://hub.docker.com/search/?type=edition&offering=community).

To build and deploy your application for the first time, run the following in your shell:

```bash
sam build
sam deploy --guided
```

The first command will build the source of your application. The second command will package and deploy your application to AWS, with a series of prompts:

* **Stack Name**: The name of the stack to deploy to CloudFormation. This should be unique to your account and region, and a good starting point would be something matching your project name.
* **AWS Region**: The AWS region you want to deploy your app to.
* **Confirm changes before deploy**: If set to yes, any change sets will be shown to you before execution for manual review. If set to no, the AWS SAM CLI will automatically deploy application changes.
* **Allow SAM CLI IAM role creation**: Many AWS SAM templates, including this example, create AWS IAM roles required for the AWS Lambda function(s) included to access AWS services. By default, these are scoped down to minimum required permissions. To deploy an AWS CloudFormation stack which creates or modifies IAM roles, the `CAPABILITY_IAM` value for `capabilities` must be provided. If permission isn't provided through this prompt, to deploy this example you must explicitly pass `--capabilities CAPABILITY_IAM` to the `sam deploy` command.
* **Save arguments to samconfig.toml**: If set to yes, your choices will be saved to a configuration file inside the project, so that in the future you can just re-run `sam deploy` without parameters to deploy changes to your application.

The API Gateway endpoint API will be displayed in the outputs when the deployment is complete.

## Use the AWS SAM CLI to build and test locally

Build your application by using the `sam build` command.

```bash
my-application$ sam build
```

The AWS SAM CLI installs dependencies that are defined in `package.json`, creates a deployment package, and saves it in the `.aws-sam/build` folder.

Test a single function by invoking it directly with a test event. An event is a JSON document that represents the input that the function receives from the event source. Test events are included in the `events` folder in this project.

Run functions locally and invoke them with the `sam local invoke` command.

```bash
my-application$ sam local invoke putItemFunction --event events/event-post-item.json
my-application$ sam local invoke getAllItemsFunction --event events/event-get-all-items.json
```

The AWS SAM CLI can also emulate your application's API. Use the `sam local start-api` command to run the API locally on port 3000.

```bash
my-application$ sam local start-api
my-application$ curl http://localhost:3000/
```

The AWS SAM CLI reads the application template to determine the API's routes and the functions that they invoke. The `Events` property on each function's definition includes the route and method for each path.

```yaml
      Events:
        Api:
          Type: Api
          Properties:
            Path: /
            Method: GET
```

## Add a resource to your application
The application template uses AWS SAM to define application resources. AWS SAM is an extension of AWS CloudFormation with a simpler syntax for configuring common serverless application resources, such as functions, triggers, and APIs. For resources that aren't included in the [AWS SAM specification](https://github.com/awslabs/serverless-application-model/blob/master/versions/2016-10-31.md), you can use the standard [AWS CloudFormation resource types](https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-template-resource-type-ref.html).

Update `template.yaml` to add a dead-letter queue to your application. In the **Resources** section, add a resource named **MyQueue** with the type **AWS::SQS::Queue**. Then add a property to the **AWS::Serverless::Function** resource named **DeadLetterQueue** that targets the queue's Amazon Resource Name (ARN), and a policy that grants the function permission to access the queue.

```
Resources:
  MyQueue:
    Type: AWS::SQS::Queue
  getAllItemsFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/get-all-items.getAllItemsHandler
      Runtime: nodejs20.x
      DeadLetterQueue:
        Type: SQS
        TargetArn: !GetAtt MyQueue.Arn
      Policies:
        - SQSSendMessagePolicy:
            QueueName: !GetAtt MyQueue.QueueName
```

The dead-letter queue is a location for Lambda to send events that could not be processed. It's only used if you invoke your function asynchronously, but it's useful here to show how you can modify your application's resources and function configuration.

Deploy the updated application.

```bash
my-application$ sam deploy
```

Open the [**Applications**](https://console.aws.amazon.com/lambda/home#/applications) page of the Lambda console, and choose your application. When the deployment completes, view the application resources on the **Overview** tab to see the new resource. Then, choose the function to see the updated configuration that specifies the dead-letter queue.

## Fetch, tail, and filter Lambda function logs

To simplify troubleshooting, the AWS SAM CLI has a command called `sam logs`. `sam logs` lets you fetch logs that are generated by your Lambda function from the command line. In addition to printing the logs on the terminal, this command has several nifty features to help you quickly find the bug.

**NOTE:** This command works for all Lambda functions, not just the ones you deploy using AWS SAM.

```bash
my-application$ sam logs -n putItemFunction --stack-name sam-app --tail
```

**NOTE:** This uses the logical name of the function within the stack. This is the correct name to use when searching logs inside an AWS Lambda function within a CloudFormation stack, even if the deployed function name varies due to CloudFormation's unique resource name generation.

You can find more information and examples about filtering Lambda function logs in the [AWS SAM CLI documentation](https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/serverless-sam-cli-logging.html).

## Unit tests

Tests are defined in the `__tests__` folder in this project. Use `npm` to install the [Jest test framework](https://jestjs.io/) and run unit tests.

```bash
my-application$ npm install
my-application$ npm run test
```

## Cleanup

To delete the sample application that you created, use the AWS CLI. Assuming you used your project name for the stack name, you can run the following:

```bash
sam delete --stack-name collections-api
```

## Resources

For an introduction to the AWS SAM specification, the AWS SAM CLI, and serverless application concepts, see the [AWS SAM Developer Guide](https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/what-is-sam.html).

Next, you can use the AWS Serverless Application Repository to deploy ready-to-use apps that go beyond Hello World samples and learn how authors developed their applications. For more information, see the [AWS Serverless Application Repository main page](https://aws.amazon.com/serverless/serverlessrepo/) and the [AWS Serverless Application Repository Developer Guide](https://docs.aws.amazon.com/serverlessrepo/latest/devguide/what-is-serverlessrepo.html).

## Starting the API locally
```bash
cd doap_mono_serverless/common/layer/database
npm install
sam build

cd doap_mono_serverless/collections-api
npm install
sam build
sam local start-api  --env-vars env.json
```

#### Institute History Management

##### Get All Institute Histories

Retrieves all institute histories with pagination and filtering.

**URL**: `/collections/institutes/hist`

**Method**: `GET`

**Query Parameters**:

**Pagination Parameters**:
- `page` (optional): Page number (default: 1)
- `pageSize` (optional): Number of items per page (default: 10, max: 100)

**Filter Parameters**:
- `collection_id` (optional): Filter by collection ID
  - Example: `collection_id=1` will return history entries for collection with ID 1
- `collection_history_id` (optional): Filter by collection history ID
  - Example: `collection_history_id=5` will return history entries for collection history with ID 5
- `institute_id` (optional): Filter by institute ID
  - Example: `institute_id=3` will return history entries for institute with ID 3
- `form_id` (optional): Filter by form ID
  - Example: `form_id=2` will return history entries for form with ID 2
- `collection_status_id` (optional): Filter by collection status ID
  - Available values:
    - `1`: Active
    - `2`: Closed
    - `3`: Pending
  - Example: `collection_status_id=1` will return history entries with Active status
- `year` (optional): Filter by year of execution date
  - Example: `year=2023` will return history entries from 2023

**Example Request**:
```bash
curl "http://localhost:3000/collections/institutes/hist?page=1&pageSize=5&collection_id=1&year=2023"
```

**Example Response**:
```json
{
  "histories": [
    {
      "id": 1,
      "collection_history_id": 1,
      "institute_id": 1,
      "form_id": 1,
      "template_form_id": 1,
      "collection_status_id": 1,
      "execution_date": "2023-01-15T00:00:00.000Z",
      "institute_name": "University A",
      "institute_email": "<EMAIL>",
      "institute_contact": "+1-555-0123",
      "form_name": "Student Enrollment Form",
      "form_file_name": "student_enrollment.pdf",
      "template_form_name": "Student Enrollment Template",
      "template_form_file_name": "student_enrollment_template.pdf",
      "status_name": "Active",
      "collection_id": 1,
      "collection_name": "Spring 2023 Data Collection",
      "collection_description": "Collection of data for Spring 2023 semester",
      "open_date": "2023-01-15T00:00:00.000Z",
      "close_date": "2023-05-15T00:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "pageSize": 5,
    "totalCount": 1,
    "totalPages": 1,
    "hasNextPage": false,
    "hasPreviousPage": false
  }
}
```

##### Get Institute Collection History by Institute ID and History ID

Retrieves collection history for a specific institute and history ID.

**URL**: `/collections/institutes/{institute}/hist/{id}`

**Method**: `GET`

**URL Parameters**:
- `institute`: The institute ID
- `id`: The collection history ID

**Example Request**:
```bash
curl "http://localhost:3000/collections/institutes/1/hist/5"
```

**Example Response**:
```json
[
  {
    "id": 1,
    "collection_history_id": 5,
    "institute_id": 1,
    "form_id": 1,
    "template_form_id": 1,
    "collection_status_id": 1,
    "execution_date": "2023-01-15T00:00:00.000Z",
    "institute_name": "University A",
    "institute_email": "<EMAIL>",
    "institute_contact": "+1-555-0123",
    "form_name": "Student Enrollment Form",
    "form_file_name": "student_enrollment.pdf",
    "template_form_name": "Student Enrollment Template",
    "template_form_file_name": "student_enrollment_template.pdf",
    "status_name": "Active",
    "collection_id": 1,
    "collection_name": "Spring 2023 Data Collection",
    "collection_description": "Collection of data for Spring 2023 semester",
    "open_date": "2023-01-15T00:00:00.000Z",
    "close_date": "2023-05-15T00:00:00.000Z",
    "download_url": "https://forms-bucket.s3.amazonaws.com/1/student_enrollment.pdf?X-Amz-Algorithm=...",
    "template_download_url": "https://forms-bucket.s3.amazonaws.com/1/student_enrollment_template.pdf?X-Amz-Algorithm=..."
  }
]
```

##### Get All Institutes Collection History by History ID

Retrieves all institutes collection history for a specific history ID.

**URL**: `/collections/institutes/hist/{id}`

**Method**: `GET`

**URL Parameters**:
- `id`: The collection history ID

**Example Request**:
```bash
curl "http://localhost:3000/collections/institutes/hist/5"
```

**Example Response**:
```json
[
  {
    "id": 1,
    "collection_history_id": 5,
    "institute_id": 1,
    "form_id": 1,
    "template_form_id": 1,
    "collection_status_id": 1,
    "execution_date": "2023-01-15T00:00:00.000Z",
    "institute_name": "University A",
    "institute_email": "<EMAIL>",
    "institute_contact": "+1-555-0123",
    "form_name": "Student Enrollment Form",
    "form_file_name": "student_enrollment.pdf",
    "template_form_name": "Student Enrollment Template",
    "template_form_file_name": "student_enrollment_template.pdf",
    "status_name": "Active",
    "collection_id": 1,
    "collection_name": "Spring 2023 Data Collection",
    "collection_description": "Collection of data for Spring 2023 semester",
    "open_date": "2023-01-15T00:00:00.000Z",
    "close_date": "2023-05-15T00:00:00.000Z"
  },
  {
    "id": 2,
    "collection_history_id": 5,
    "institute_id": 2,
    "form_id": 1,
    "template_form_id": 1,
    "collection_status_id": 1,
    "execution_date": "2023-01-15T00:00:00.000Z",
    "institute_name": "College B",
    "institute_email": "<EMAIL>",
    "institute_contact": "+1-555-0456",
    "form_name": "Student Enrollment Form",
    "form_file_name": "student_enrollment.pdf",
    "template_form_name": "Student Enrollment Template",
    "template_form_file_name": "student_enrollment_template.pdf",
    "status_name": "Active",
    "collection_id": 1,
    "collection_name": "Spring 2023 Data Collection",
    "collection_description": "Collection of data for Spring 2023 semester",
    "open_date": "2023-01-15T00:00:00.000Z",
    "close_date": "2023-05-15T00:00:00.000Z"
  }
]
```

##### Update Collection Institute History

Updates a collection institute history record.

**URL**: `/collections/institutes/forms/hist/{id}`

**Method**: `PUT`

**URL Parameters**:
- `id`: The collection institute history record ID

**Request Body**:
```json
{
  "collection_history_id": number,  // Optional
  "institute_id": number,           // Optional
  "form_id": number,                // Optional
  "template_form_id": number,       // Optional
  "collection_status_id": number,   // Optional
  "execution_date": string          // Optional - ISO date string
}
```

**Example Request**:
```bash
curl -X PUT "http://localhost:3000/collections/institutes/forms/hist/1" \
  -H "Content-Type: application/json" \
  -d '{
    "collection_status_id": 2,
    "execution_date": "2023-01-20T00:00:00.000Z"
  }'
```

**Example Response**:
```json
{
  "id": 1,
  "collection_history_id": 5,
  "institute_id": 1,
  "form_id": 1,
  "template_form_id": 1,
  "collection_status_id": 2,
  "execution_date": "2023-01-20T00:00:00.000Z"
}
```