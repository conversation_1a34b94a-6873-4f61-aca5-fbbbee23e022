AWSTemplateFormatVersion: 2010-09-09
Description: >-
  collections-api
Transform:
  - AWS::Serverless-2016-10-31

Parameters:
  Stage:
    Type: String
    Default: dev
    AllowedValues:
      - prod
      - dev
      - qa
    Description: Deployment stage (prod/dev/qa)

  BucketName:
    Type: String
    Default: ""
    Description: Name of the S3 bucket for storing forms

  DefaultFromEmail:
    Type: String
    Default: <EMAIL>
    Description: Default sender email address

# Resources declares the AWS resources that you want to include in the stack
Resources:
  # Database layer definition
  DoapDatabaseLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: !Sub "doap-database-layer-${Stage}"
      Description: Common DB logic
      ContentUri: ../common/layer/database
      CompatibleRuntimes:
        - nodejs20.x
      RetentionPolicy: Retain

  # Email layer definition
  DoapEmailLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: !Sub "doap-email-layer-${Stage}"
      Description: Email service using AWS SES
      ContentUri: ../common/layer/email
      CompatibleRuntimes:
        - nodejs20.x
      RetentionPolicy: Retain

  # Forms S3 Bucket
  FormsBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !If 
        - UseDefaultBucketName
        - !Sub doap-forms-bucket-${AWS::AccountId}-${Stage}
        - !Ref BucketName
      VersioningConfiguration:
        Status: Enabled
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256
      LifecycleConfiguration:
        Rules:
          - Id: DeleteOldVersions
            Status: Enabled
            NoncurrentVersionExpiration:
              NoncurrentDays: 30

  # Bucket Policy
  FormsBucketPolicy:
    Type: AWS::S3::BucketPolicy
    Properties:
      Bucket: !Ref FormsBucket
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Sid: EnforceSSLOnly
            Effect: Deny
            Principal: '*'
            Action: 's3:*'
            Resource: 
              - !Sub 'arn:aws:s3:::${FormsBucket}'
              - !Sub 'arn:aws:s3:::${FormsBucket}/*'
            Condition:
              Bool:
                aws:SecureTransport: false

  # Each Lambda function is defined by properties:
  # https://github.com/awslabs/serverless-application-model/blob/master/versions/2016-10-31.md#awsserverlessfunction

  # This is a Lambda function config associated with the source code: get-all-items.js
  getAllItemsFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/get-all-items.getAllItemsHandler
      Runtime: nodejs20.x
      Environment:
        Variables:
          DATABASE_URL: !If 
            - IsProd
            - "postgres://doapadmin:<EMAIL>:5432/doapDb"
            - "postgres://doapadmin:<EMAIL>:5432/devtest"
          FORMS_BUCKET_NAME: !Ref FormsBucket
      Architectures:
        - x86_64
      MemorySize: 128
      Timeout: 100
      Description: get all collections from array.
      Layers:
        - !Ref DoapDatabaseLayer
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - s3:GetObject
                - s3:PutObject
                - s3:DeleteObject
                - s3:ListBucket
              Resource:
                - !Sub 'arn:aws:s3:::${FormsBucket}'
                - !Sub 'arn:aws:s3:::${FormsBucket}/*'
      Events:
        Api:
          Type: Api
          Properties:
            Path: /collections
            Method: GET
            RestApiId: !Ref ApiGateway

  # This is a Lambda function config associated with the source code: get-by-id.js
  getByIdFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/get-by-id.getByIdHandler
      Runtime: nodejs20.x
      Environment:
        Variables:
          DATABASE_URL: !If 
            - IsProd
            - "postgres://doapadmin:<EMAIL>:5432/doapDb"
            - "postgres://doapadmin:<EMAIL>:5432/devtest"
          FORMS_BUCKET_NAME: !Ref FormsBucket
      Architectures:
        - x86_64
      MemorySize: 128
      Timeout: 100
      Description: A simple example includes a HTTP get method to get one item by id from database
      Layers:
        - !Ref DoapDatabaseLayer
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - s3:GetObject
                - s3:PutObject
                - s3:DeleteObject
                - s3:ListBucket
              Resource:
                - !Sub 'arn:aws:s3:::${FormsBucket}'
                - !Sub 'arn:aws:s3:::${FormsBucket}/*'
      Events:
        Api:
          Type: Api
          Properties:
            Path: /collections/{id}
            Method: GET
            RestApiId: !Ref ApiGateway

  # This is a Lambda function config associated with the source code: put-item.js
  putItemFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/put-item.putItemHandler
      Runtime: nodejs20.x
      Environment:
        Variables:
          DATABASE_URL: !If 
            - IsProd
            - "postgres://doapadmin:<EMAIL>:5432/doapDb"
            - "postgres://doapadmin:<EMAIL>:5432/devtest"
          FORMS_BUCKET_NAME: !Ref FormsBucket
      Architectures:
        - x86_64
      MemorySize: 128
      Timeout: 100
      Description: A simple example includes a HTTP post method to add one item to database.
      Layers:
        - !Ref DoapDatabaseLayer
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - s3:GetObject
                - s3:PutObject
                - s3:DeleteObject
                - s3:ListBucket
              Resource:
                - !Sub 'arn:aws:s3:::${FormsBucket}'
                - !Sub 'arn:aws:s3:::${FormsBucket}/*'
      Events:
        Api:
          Type: Api
          Properties:
            Path: /collections
            Method: POST
            RestApiId: !Ref ApiGateway

  # This is a Lambda function config for getting all forms
  getAllFormsFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/get-all-forms.getAllFormsHandler
      Runtime: nodejs20.x
      Environment:
        Variables:
          DATABASE_URL: !If 
            - IsProd
            - "postgres://doapadmin:<EMAIL>:5432/doapDb"
            - "postgres://doapadmin:<EMAIL>:5432/devtest"
          FORMS_BUCKET_NAME: !Ref FormsBucket
      Architectures:
        - x86_64
      MemorySize: 128
      Timeout: 100
      Description: Get all forms from the database
      Layers:
        - !Ref DoapDatabaseLayer
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - s3:GetObject
                - s3:PutObject
                - s3:DeleteObject
                - s3:ListBucket
              Resource:
                - !Sub 'arn:aws:s3:::${FormsBucket}'
                - !Sub 'arn:aws:s3:::${FormsBucket}/*'
      Events:
        Api:
          Type: Api
          Properties:
            Path: /collections/forms
            Method: GET
            RestApiId: !Ref ApiGateway

  # This is a Lambda function config for getting all collection statuses
  getAllStatusesFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/get-all-statuses.getAllStatusesHandler
      Runtime: nodejs20.x
      Environment:
        Variables:
          DATABASE_URL: !If 
            - IsProd
            - "postgres://doapadmin:<EMAIL>:5432/doapDb"
            - "postgres://doapadmin:<EMAIL>:5432/devtest"
      Architectures:
        - x86_64
      MemorySize: 128
      Timeout: 100
      Description: Get all collection statuses from the database
      Layers:
        - !Ref DoapDatabaseLayer
      Events:
        Api:
          Type: Api
          Properties:
            Path: /collections/statuses
            Method: GET
            RestApiId: !Ref ApiGateway

  # This is a Lambda function config for getting all collection windows
  getAllWindowsFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/get-all-windows.getAllWindowsHandler
      Runtime: nodejs20.x
      Environment:
        Variables:
          DATABASE_URL: !If 
            - IsProd
            - "postgres://doapadmin:<EMAIL>:5432/doapDb"
            - "postgres://doapadmin:<EMAIL>:5432/devtest"
      Architectures:
        - x86_64
      MemorySize: 128
      Timeout: 100
      Description: Get all collection windows from the database
      Layers:
        - !Ref DoapDatabaseLayer
      Events:
        Api:
          Type: Api
          Properties:
            Path: /collections/windows
            Method: GET
            RestApiId: !Ref ApiGateway

  # This is a Lambda function config for getting all collection frequencies
  getAllFrequenciesFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/get-all-frequencies.getAllFrequenciesHandler
      Runtime: nodejs20.x
      Environment:
        Variables:
          DATABASE_URL: !If 
            - IsProd
            - "postgres://doapadmin:<EMAIL>:5432/doapDb"
            - "postgres://doapadmin:<EMAIL>:5432/devtest"
      Architectures:
        - x86_64
      MemorySize: 128
      Timeout: 100
      Description: Get all collection frequencies from the database
      Layers:
        - !Ref DoapDatabaseLayer
      Events:
        Api:
          Type: Api
          Properties:
            Path: /collections/frequencies
            Method: GET
            RestApiId: !Ref ApiGateway

  # This is a Lambda function config for getting all collection types
  getAllTypesFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/get-all-types.getAllTypesHandler
      Runtime: nodejs20.x
      Environment:
        Variables:
          DATABASE_URL: !If 
            - IsProd
            - "postgres://doapadmin:<EMAIL>:5432/doapDb"
            - "postgres://doapadmin:<EMAIL>:5432/devtest"
      Architectures:
        - x86_64
      MemorySize: 128
      Timeout: 100
      Description: Get all collection types from the database
      Layers:
        - !Ref DoapDatabaseLayer
      Events:
        Api:
          Type: Api
          Properties:
            Path: /collections/types
            Method: GET
            RestApiId: !Ref ApiGateway

  # This is a Lambda function config for getting collection history
  getCollectionHistoryFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/get-collection-history.getCollectionHistoryHandler
      Runtime: nodejs20.x
      Environment:
        Variables:
          DATABASE_URL: !If 
            - IsProd
            - "postgres://doapadmin:<EMAIL>:5432/doapDb"
            - "postgres://doapadmin:<EMAIL>:5432/devtest"
          FORMS_BUCKET_NAME: !Ref FormsBucket
      Architectures:
        - x86_64
      MemorySize: 128
      Timeout: 100
      Description: Get history entries for a specific collection
      Layers:
        - !Ref DoapDatabaseLayer
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - s3:GetObject
                - s3:PutObject
                - s3:DeleteObject
                - s3:ListBucket
              Resource:
                - !Sub 'arn:aws:s3:::${FormsBucket}'
                - !Sub 'arn:aws:s3:::${FormsBucket}/*'
      Events:
        Api:
          Type: Api
          Properties:
            Path: /collections/{id}/history
            Method: GET
            RestApiId: !Ref ApiGateway

  # This is a Lambda function config for getting all collection histories
  getAllHistoriesFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/get-all-histories.getAllHistoriesHandler
      Runtime: nodejs20.x
      Environment:
        Variables:
          DATABASE_URL: !If 
            - IsProd
            - "postgres://doapadmin:<EMAIL>:5432/doapDb"
            - "postgres://doapadmin:<EMAIL>:5432/devtest"
          FORMS_BUCKET_NAME: !Ref FormsBucket
      Architectures:
        - x86_64
      MemorySize: 128
      Timeout: 100
      Description: Get all collection histories with filtering and pagination
      Layers:
        - !Ref DoapDatabaseLayer
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - s3:GetObject
                - s3:PutObject
                - s3:DeleteObject
                - s3:ListBucket
              Resource:
                - !Sub 'arn:aws:s3:::${FormsBucket}'
                - !Sub 'arn:aws:s3:::${FormsBucket}/*'
      Events:
        Api:
          Type: Api
          Properties:
            Path: /collections/history
            Method: GET
            RestApiId: !Ref ApiGateway

  # This is a Lambda function config for getting a form by ID
  getFormByIdFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/get-form-by-id.getFormByIdHandler
      Runtime: nodejs20.x
      Environment:
        Variables:
          DATABASE_URL: !If 
            - IsProd
            - "postgres://doapadmin:<EMAIL>:5432/doapDb"
            - "postgres://doapadmin:<EMAIL>:5432/devtest"
          FORMS_BUCKET_NAME: !Ref FormsBucket
      Architectures:
        - x86_64
      MemorySize: 128
      Timeout: 100
      Description: Get a specific form by ID from the database
      Layers:
        - !Ref DoapDatabaseLayer
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - s3:GetObject
                - s3:PutObject
                - s3:DeleteObject
                - s3:ListBucket
              Resource:
                - !Sub 'arn:aws:s3:::${FormsBucket}'
                - !Sub 'arn:aws:s3:::${FormsBucket}/*'
      Events:
        Api:
          Type: Api
          Properties:
            Path: /collections/forms/{id}
            Method: GET
            RestApiId: !Ref ApiGateway

  GetCollectionInstituteHistoryByIdFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/get-collection-institute-history.getCollectionInstituteHistoryHandler
      Runtime: nodejs20.x
      Environment:
        Variables:
          DATABASE_URL: !If 
            - IsProd
            - "postgres://doapadmin:<EMAIL>:5432/doapDb"
            - "postgres://doapadmin:<EMAIL>:5432/devtest"
          FORMS_BUCKET_NAME: !Ref FormsBucket
      Architectures:
        - x86_64
      MemorySize: 256
      Timeout: 30
      Description: Get collection history for a specific institute
      Layers:
        - !Ref DoapDatabaseLayer
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - s3:GetObject
                - s3:PutObject
                - s3:DeleteObject
                - s3:ListBucket
              Resource:
                - !Sub 'arn:aws:s3:::${FormsBucket}'
                - !Sub 'arn:aws:s3:::${FormsBucket}/*'
      Events:
        Api:
          Type: Api
          Properties:
            Path: /collections/institutes/{institute}/hist/{id}
            Method: GET
            RestApiId: !Ref ApiGateway

  GetAllInstituteHistoryFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/get-all-institute-history.getAllInstituteHistoryHandler
      Runtime: nodejs20.x
      Environment:
        Variables:
          DATABASE_URL: !If 
            - IsProd
            - "postgres://doapadmin:<EMAIL>:5432/doapDb"
            - "postgres://doapadmin:<EMAIL>:5432/devtest"
          FORMS_BUCKET_NAME: !Ref FormsBucket
      Architectures:
        - x86_64
      MemorySize: 256
      Timeout: 30
      Description: Get all institute histories with pagination and filtering
      Layers:
        - !Ref DoapDatabaseLayer
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - s3:GetObject
                - s3:PutObject
                - s3:DeleteObject
                - s3:ListBucket
              Resource:
                - !Sub 'arn:aws:s3:::${FormsBucket}'
                - !Sub 'arn:aws:s3:::${FormsBucket}/*'
      Events:
        Api:
          Type: Api
          Properties:
            Path: /collections/institutes/hist
            Method: GET
            RestApiId: !Ref ApiGateway

  optionsFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/options-handler.optionsHandler
      Runtime: nodejs20.x
      Architectures:
        - x86_64
      MemorySize: 128
      Timeout: 100
      Description: Handles OPTIONS requests for CORS preflight
      Layers:
        - !Ref DoapDatabaseLayer
      Events:
        ApiCollections:
          Type: Api
          Properties:
            Path: /collections
            Method: OPTIONS
            RestApiId: !Ref ApiGateway
        ApiInstituteById:
          Type: Api
          Properties:
            Path: /collections/{id}
            Method: OPTIONS
            RestApiId: !Ref ApiGateway
        ApiForms:
          Type: Api
          Properties:
            Path: /collections/forms
            Method: OPTIONS
            RestApiId: !Ref ApiGateway
        ApiFormById:
          Type: Api
          Properties:
            Path: /collections/forms/{id}
            Method: OPTIONS
            RestApiId: !Ref ApiGateway
        ApiCollectionStatuses:
          Type: Api
          Properties:
            Path: /collections/statuses
            Method: OPTIONS
            RestApiId: !Ref ApiGateway
        ApiCollectionWindows:
          Type: Api
          Properties:
            Path: /collections/windows
            Method: OPTIONS
            RestApiId: !Ref ApiGateway
        ApiCollectionFrequencies:
          Type: Api
          Properties:
            Path: /collections/frequencies
            Method: OPTIONS
            RestApiId: !Ref ApiGateway
        ApiCollectionTypes:
          Type: Api
          Properties:
            Path: /collections/types
            Method: OPTIONS
            RestApiId: !Ref ApiGateway
        ApiCollectionHistory:
          Type: Api
          Properties:
            Path: /collections/{id}/history
            Method: OPTIONS
            RestApiId: !Ref ApiGateway
        ApiAllCollectionHistories:
          Type: Api
          Properties:
            Path: /collections/history
            Method: OPTIONS
            RestApiId: !Ref ApiGateway
        ApiInstitutesCollectionHistory:
          Type: Api
          Properties:
            Path: /collections/institutes/hist/{id}
            Method: OPTIONS
            RestApiId: !Ref ApiGateway
        ApiCollectionInstituteHistory:
          Type: Api
          Properties:
            Path: /collections/institutes/{institute}/hist/{id}
            Method: OPTIONS
            RestApiId: !Ref ApiGateway
        ApiAllInstituteHistory:
          Type: Api
          Properties:
            Path: /collections/institutes/hist
            Method: OPTIONS
            RestApiId: !Ref ApiGateway
        ApiSubmitCollectionInstituteHistory:
          Type: Api
          Properties:
            Path: /collections/institutes/forms/submit/{id}
            Method: OPTIONS
            RequestParameters:
              - method.request.path.id: true
        ApiSaveCollectionInstituteHistoryDraft:
          Type: Api
          Properties:
            Path: /collections/institutes/forms/draft/{id}
            Method: OPTIONS
            RequestParameters:
              - method.request.path.id: true

  UploadFormsFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: src/
      Handler: handlers/upload-forms.uploadFormsHandler
      Runtime: nodejs20.x
      Environment:
        Variables:
          DATABASE_URL: !If 
            - IsProd
            - "postgres://doapadmin:<EMAIL>:5432/doapDb"
            - "postgres://doapadmin:<EMAIL>:5432/devtest"
          FORMS_BUCKET_NAME: !Ref FormsBucket
      Architectures:
        - x86_64
      MemorySize: 128
      Timeout: 30
      Description: Upload forms to S3 bucket and update database
      Layers:
        - !Ref DoapDatabaseLayer
      Events:
        Api:
          Type: Api
          Properties:
            Path: /collections/forms
            Method: POST
            RestApiId: !Ref ApiGateway
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - s3:PutObject
                - s3:GetObject
                - s3:DeleteObject
                - s3:ListBucket
              Resource:
                - !Sub "arn:aws:s3:::${FormsBucket}"
                - !Sub "arn:aws:s3:::${FormsBucket}/*"

  ApiGateway:
    Type: AWS::Serverless::Api
    Properties:
      StageName: !Ref Stage
      EndpointConfiguration: 
        Type: EDGE
      TracingEnabled: true
      # Remove the MethodSettings section entirely to avoid logging configuration
  ApplicationResourceGroup:
    Type: AWS::ResourceGroups::Group
    Properties:
      Name:
        Fn::Sub: ApplicationInsights-SAM-${AWS::StackName}
      ResourceQuery:
        Type: CLOUDFORMATION_STACK_1_0
  ApplicationInsightsMonitoring:
    Type: AWS::ApplicationInsights::Application
    Properties:
      ResourceGroupName:
        Ref: ApplicationResourceGroup
      AutoConfigurationEnabled: "true"

  # This is a Lambda function config for recurring schedule processing
  remindersRecurringScheduleFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/reminders-handler.remindersHandler
      Runtime: nodejs20.x
      Environment:
        Variables:
          DATABASE_URL: !If 
            - IsProd
            - "postgres://doapadmin:<EMAIL>:5432/doapDb"
            - "postgres://doapadmin:<EMAIL>:5432/devtest"
          DEFAULT_REGION: !Ref AWS::Region
          DEFAULT_FROM_EMAIL: !Ref DefaultFromEmail
      Architectures:
        - x86_64
      MemorySize: 128
      Timeout: 300
      Description: Processes recurring schedules for data collections
      Layers:
        - !Ref DoapDatabaseLayer
        - !Ref DoapEmailLayer
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - ses:SendEmail
                - ses:SendRawEmail
              Resource: '*'
      Events:
        ScheduleRule:
          Type: Schedule
          Properties:
            Description: Trigger reminders every 5 minutes
            Enabled: true
            Schedule: rate(5 minutes)
            Name: !Sub ${AWS::StackName}-reminders-${Stage}

  collectionsRecurringScheduleFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/collections-handler.collectionsHandler
      Runtime: nodejs20.x
      Timeout: 300
      MemorySize: 256
      Environment:
        Variables:
          DATABASE_URL: !If 
            - IsProd
            - "postgres://doapadmin:<EMAIL>:5432/doapDb"
            - "postgres://doapadmin:<EMAIL>:5432/devtest"
          DEFAULT_REGION: !Ref AWS::Region
          STAGE: !Ref Stage
      Architectures:
        - x86_64
      Description: Process collections daily
      Layers:
        - !Ref DoapDatabaseLayer
      Events:
        CollectionsSchedule:
          Type: Schedule
          Properties:
            Schedule: rate(5 minutes)
            Description: Process collections daily
            Enabled: true
            Name: !Sub ${AWS::StackName}-collections-${Stage}

  GetAllInstitutesCollectionHistoryByIdFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/get-all-institutes-collection-history.getAllInstitutesCollectionHistoryHandler
      Runtime: nodejs20.x
      Environment:
        Variables:
          DATABASE_URL: !If 
            - IsProd
            - "postgres://doapadmin:<EMAIL>:5432/doapDb"
            - "postgres://doapadmin:<EMAIL>:5432/devtest"
          FORMS_BUCKET_NAME: !Ref FormsBucket
      Architectures:
        - x86_64
      MemorySize: 256
      Timeout: 30
      Description: Get all institutes collection history for a specific history ID
      Layers:
        - !Ref DoapDatabaseLayer
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - s3:GetObject
                - s3:PutObject
                - s3:DeleteObject
                - s3:ListBucket
              Resource:
                - !Sub 'arn:aws:s3:::${FormsBucket}'
                - !Sub 'arn:aws:s3:::${FormsBucket}/*'
      Events:
        Api:
          Type: Api
          Properties:
            Path: /collections/institutes/hist/{id}
            Method: GET
            RestApiId: !Ref ApiGateway

  SubmitCollectionInstituteHistoryFormFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/submit-form.submitFormHandler
      Runtime: nodejs20.x
      Architectures:
        - x86_64
      MemorySize: 256
      Timeout: 30
      Environment:
        Variables:
          DATABASE_URL: !If 
            - IsProd
            - "postgres://doapadmin:<EMAIL>:5432/doapDb"
            - "postgres://doapadmin:<EMAIL>:5432/devtest"
          FORMS_BUCKET_NAME: !Ref FormsBucket
      Layers:
        - !Ref DoapDatabaseLayer
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - s3:GetObject
                - s3:PutObject
                - s3:DeleteObject
                - s3:ListBucket
              Resource:
                - !Sub 'arn:aws:s3:::${FormsBucket}'
                - !Sub 'arn:aws:s3:::${FormsBucket}/*'
      Events:
        ApiUpdateCollectionInstituteHistory:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /collections/institutes/forms/submit/{id}
            Method: PUT
            RequestParameters:
              - method.request.path.id: true

  SaveCollectionInstituteHistoryDraftFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/save-form-draft.saveFormDraftHandler
      Runtime: nodejs20.x
      Architectures:
        - x86_64
      MemorySize: 256
      Timeout: 30
      Environment:
        Variables:
          DATABASE_URL: !If 
            - IsProd
            - "postgres://doapadmin:<EMAIL>:5432/doapDb"
            - "postgres://doapadmin:<EMAIL>:5432/devtest"
          FORMS_BUCKET_NAME: !Ref FormsBucket
      Layers:
        - !Ref DoapDatabaseLayer
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - s3:GetObject
                - s3:PutObject
                - s3:DeleteObject
                - s3:ListBucket
              Resource:
                - !Sub 'arn:aws:s3:::${FormsBucket}'
                - !Sub 'arn:aws:s3:::${FormsBucket}/*'
      Events:
        Api:
          Type: Api
          Properties:
            Path: /collections/institutes/forms/draft/{id}
            Method: PUT
            RestApiId: !Ref ApiGateway

Conditions:
  IsProd: !Equals [!Ref Stage, "prod"]
  IsDev: !Equals [!Ref Stage, "dev"]
  IsQa: !Equals [!Ref Stage, "qa"]
  UseDefaultBucketName: !Equals [!Ref BucketName, ""]

Outputs:
  ApiGatewayRestApiId:
    Description: The ID of the API Gateway created by SAM
    Value: !Ref ApiGateway
    Export:
      Name: !Sub "${AWS::StackName}-ApiGatewayRestApiId-${Stage}"
  WebEndpoint:
    Description: API Gateway endpoint URL for the specified stage
    Value: !Sub "https://${ApiGateway}.execute-api.${AWS::Region}.amazonaws.com/${Stage}/"
  FormsBucketName:
    Description: "Name of the S3 bucket for storing forms"
    Value: !Ref FormsBucket
    Export:
      Name: !Sub "${AWS::StackName}-FormsBucketName-${Stage}"
  FormsBucketArn:
    Description: "ARN of the S3 bucket for storing forms"
    Value: !GetAtt FormsBucket.Arn
    Export:
      Name: !Sub "${AWS::StackName}-FormsBucketArn-${Stage}"
# # More info about Globals: https://github.com/awslabs/serverless-application-model/blob/master/docs/globals.rst
Globals:
  Function:
    Tracing: Active
    LoggingConfig:
      LogFormat: JSON
  Api:
    TracingEnabled: true
