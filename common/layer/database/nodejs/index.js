/**
 * Database layer for DOAP services
 *
 * This module exports the database client, schema, and operations
 * for use across multiple Lambda functions.
 */

import { createDbClient, createDbClientFromEnv } from './client.js';
import { instituteOperations } from './institute-operations.js';
import { collectionOperations } from './collection-operations.js';
import { authOperations } from './auth-operations.js';
import schema, { InstituteTable, Tables } from './schema.js';

// Create a singleton database client using environment variables
let db;

// Lazy initialization of the database client
const getDb = () => {
  if (!db) {
    db = createDbClientFromEnv();
  }
  return db;
};

// Create operations for the institutes table
const getInstituteOperations = () => {
  return instituteOperations(getDb());
};

// Create operations for the collection tables
const getCollectionOperations = () => {
  return collectionOperations(getDb());
};

// Create operations for the auth-related tables
const getAuthOperations = () => {
  return authOperations(getDb());
};

// Export everything
export {
  // Database client
  getDb,
  createDbClient,
  createDbClientFromEnv,

  // Schema
  schema,
  InstituteTable,
  Tables,

  // Operations
  instituteOperations,
  getInstituteOperations,
  collectionOperations,
  getCollectionOperations,
  authOperations,
  getAuthOperations
};

// Default export is the database client getter
export default getDb;
