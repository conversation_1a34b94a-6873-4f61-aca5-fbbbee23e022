export const authOperations = (db) => ({
  /**
   * Save a user invitation to the database
   * @param {Object} invitation - { id, email, institute_id, roles, created_at, expire_after }
   * @returns {Promise<void>}
   */
  inviteUser: async ({ id, email, institute_id, roles, created_at, expire_after }) => {
    await db.insertInto('user_invitations').values({
      id,
      email,
      institute_id,
      roles: Array.isArray(roles) ? roles.join(',') : roles,
      created_at,
      expire_after
    }).execute();
  },
  /**
   * Verify a user invitation by id and email, and check expiry
   * @param {Object} params - { id, email }
   * @returns {Promise<Object|null>} The invitation record if valid, otherwise null
   */
  verifyInvitation: async ({ id, email }) => {
    const result = await db
      .selectFrom('user_invitations')
      .selectAll()
      .where('id', '=', id)
      .where('email', '=', email)
      .executeTakeFirst();
    if (!result) return null;
    // Check expiry
    if (result.expire_after && new Date(result.expire_after) < new Date()) {
      return null;
    }
    return result;
  },
  /**
   * Mark a user invitation as accepted
   * @param {Object} params - { id, email }
   * @returns {Promise<void>}
   */
  markInvitationAccepted: async ({ id, email }) => {
    await db.updateTable('user_invitations')
      .set({ invitation_accepted: true })
      .where('id', '=', id)
      .where('email', '=', email)
      .execute();
  },
  /**
   * Get all roles present in the system
   * @returns {Promise<Array>} Array of role objects
   */
  getAllRoles: async () => {
    return await db
      .selectFrom('roles')
      .selectAll()
      .execute();
  },
  /**
   * Get a valid, unexpired, and accepted invitation
   * @param {string} id - The invitation ID
   * @returns {Promise<Object|null>} The invitation record if valid, otherwise null
   */
  getValidInvitation: async (id) => {
    const result = await db
      .selectFrom('user_invitations')
      .selectAll()
      .where('id', '=', id)
      .where('invitation_accepted', '=', true)
      .where('expire_after', '>', new Date())
      .executeTakeFirst();
    if (result && typeof result.roles === 'string') {
      result.roles = result.roles.split(',').map(r => r.trim()).filter(Boolean);
    }
    return result;
  },

  /**
   * Create a new user in the local database
   * @param {Object} user - { id, cognito_sub, name, email, institute_id }
   * @returns {Promise<Object>} The created user record
   */
  createUser: async ({ id, cognito_sub, name, email, institute_id }) => {
    return await db.insertInto('users').values({
      id,
      cognito_sub,
      name,
      email,
      institute_id,
      enabled: true
    }).returningAll().executeTakeFirst();
  },

  /**
   * Assign roles to a user
   * @param {string} userId - The user's UUID
   * @param {Array<number>} roleIds - Array of role IDs to assign
   * @returns {Promise<void>}
   */
  assignUserRoles: async (userId, roleIds) => {
    const assignments = roleIds.map(roleId => ({ user_id: userId, role_id: roleId }));
    await db.insertInto('user_roles').values(assignments).execute();
  },

  /**
   * Delete a used invitation
   * @param {string} id - The invitation ID
   * @returns {Promise<void>}
   */
  deleteInvitation: async (id) => {
    await db.deleteFrom('user_invitations').where('id', '=', id).execute();
  }
}); 