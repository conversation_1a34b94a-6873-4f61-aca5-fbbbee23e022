import { InstituteTable } from './schema.js';

/**
 * Create database operations for the institutes table
 *
 * @param {Kysely} db - Kysely database client instance
 * @returns {Object} - Object containing database operations
 */
export const instituteOperations = (db) => ({
  /**
   * Get all institutes from the database
   * @returns {Promise<Array>} Array of institute objects
   */
  getAllInstitutes: async () => {
    return await db
      .selectFrom(InstituteTable.tableName)
      .selectAll()
      .execute();
  },

  /**
   * Get paginated institutes from the database with optional filters
   * @param {number} offset - Number of records to skip
   * @param {number} limit - Maximum number of records to return
   * @param {Object} filters - Optional filters: name, email, contact_number
   * @param {Array} fields - Optional array of columns to select
   * @returns {Promise<Array>} Array of institute objects
   */
  getPaginatedInstitutes: async (offset, limit, filters = {}, fields = null) => {
    let query = db
      .selectFrom(InstituteTable.tableName);
    if (fields && Array.isArray(fields) && fields.length > 0) {
      query = query.select(fields);
    } else {
      query = query.selectAll();
    }
    if (filters.name) {
      query = query.where('name', 'ilike', `%${filters.name}%`);
    }
    if (filters.email) {
      query = query.where('email', 'ilike', `%${filters.email}%`);
    }
    if (filters.contact_number) {
      query = query.where('contact_number', 'ilike', `%${filters.contact_number}%`);
    }
    return await query
      .offset(offset)
      .limit(limit)
      .execute();
  },

  /**
   * Get total count of institutes with optional filters
   * @param {Object} filters - Optional filters: name, email, contact_number
   * @returns {Promise<number>} Total count of institutes
   */
  getInstitutesTotalCount: async (filters = {}) => {
    let query = db
      .selectFrom(InstituteTable.tableName)
      .select(db.fn.count('id').as('count'));
    if (filters.name) {
      query = query.where('name', 'ilike', `%${filters.name}%`);
    }
    if (filters.email) {
      query = query.where('email', 'ilike', `%${filters.email}%`);
    }
    if (filters.contact_number) {
      query = query.where('contact_number', 'ilike', `%${filters.contact_number}%`);
    }
    const result = await query.executeTakeFirst();
    return parseInt(result.count, 10);
  },

  /**
   * Get an institute by ID
   * @param {number} id - The institute ID
   * @returns {Promise<Object|null>} Institute object or null if not found
   */
  getInstituteById: async (id) => {
    return await db
      .selectFrom(InstituteTable.tableName)
      .selectAll()
      .where('id', '=', id)
      .executeTakeFirst();
  },

  /**
   * Create a new institute
   * @param {Object} institute - The institute object to create
   * @returns {Promise<Object>} Result of the operation
   */
  createInstitute: async (institute) => {
    const { name, logo, contact_number, email } = institute;

    // Create values object with required fields and any provided updates
    const values = {};
    if (name) values.name = name;
    if (logo) values.logo = logo;
    if (contact_number) values.contact_number = contact_number;
    if (email) values.email = email;

    return await db
      .insertInto(InstituteTable.tableName)
      .values(values)
      .returning(['id', 'name', 'email', 'contact_number'])
      .executeTakeFirst();
  },

  /**
   * Update an existing institute
   * @param {Object} institute - The institute object to update
   * @returns {Promise<Object>} Result of the operation
   */
  updateInstitute: async (institute) => {
    const { id, name, email, logo, contact_number } = institute;

    // Create update object with provided fields
    const updateValues = {};
    if (name) updateValues.name = name;
    if (logo) updateValues.logo = logo;
    if (email) updateValues.email = email;
    if (contact_number) updateValues.contact_number = contact_number;

    return await db
      .updateTable(InstituteTable.tableName)
      .set(updateValues)
      .where('id', '=', id)
      .returning(['id', 'name', 'email', 'contact_number'])
      .executeTakeFirst();
  },

  /**
   * Search institutes by name
   * @param {string} searchTerm - Search term to match against institute names
   * @returns {Promise<Array>} Array of matching institute objects
   */
  searchInstitutesByName: async (searchTerm) => {
    return await db
      .selectFrom(InstituteTable.tableName)
      .selectAll()
      .where('name', 'ilike', `%${searchTerm}%`)
      .execute();
  }
});
