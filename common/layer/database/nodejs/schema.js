/**
 * Database schema definition for <PERSON>ysely
 *
 * This file defines the TypeScript interfaces that represent our database schema.
 * These interfaces are used by <PERSON><PERSON><PERSON> to provide type-safe database operations.
 */

/**
 * Table definitions with column names for easier reference
 */
export const Tables = {
  // Institute table
  InstituteTable: {
    tableName: 'institutes',
    columns: {
      id: 'id',
      name: 'name',
      logo: 'logo',
      contact_number: 'contact_number',
      email: 'email'
    }
  },

  // Collection Status table
  CollectionStatusTable: {
    tableName: 'collection_status',
    columns: {
      id: 'id',
      name: 'name',
      description: 'description'
    }
  },

  // Collection Type table
  CollectionTypeTable: {
    tableName: 'collection_type',
    columns: {
      id: 'id',
      name: 'name',
      description: 'description'
    }
  },

  // Form table
  FormTable: {
    tableName: 'form',
    columns: {
      id: 'id',
      name: 'name',
      description: 'description',
      is_template: 'is_template',
      file_name: 'file_name',
      manual_entry_mappings: 'manual_entry_mappings',
      template_form_mappings: 'template_form_mappings'
    }
  },

  // Frequency table
  FrequencyTable: {
    tableName: 'frequency',
    columns: {
      id: 'id',
      name: 'name',
      description: 'description'
    }
  },

  // Collection Window table
  CollectionWindowTable: {
    tableName: 'collection_window',
    columns: {
      id: 'id',
      name: 'name',
      description: 'description'
    }
  },

  // Data Collection table
  DataCollectionTable: {
    tableName: 'data_collection',
    columns: {
      id: 'id',
      name: 'name',
      description: 'description',
      collectionTypeId: 'collection_type_id',
      collectionWindowId: 'collection_window_id',
      emailFrequencyId: 'email_frequency_id',
      warningEmail: 'warning_email',
      warningEmailThreshold: 'warning_email_threshold',
      warningEmailMessage: 'warning_email_message',
      reminderEmails: 'reminder_emails'
    }
  },

  // Collection Institute table (junction table)
  CollectionInstituteTable: {
    tableName: 'collection_institute',
    columns: {
      collectionId: 'collection_id',
      instituteId: 'institute_id'
    }
  },

  // Collection Form table (junction table)
  CollectionFormTable: {
    tableName: 'collection_form',
    columns: {
      collectionId: 'collection_id',
      formId: 'form_id'
    }
  },

  // Collection History table
  CollectionHistoryTable: {
    tableName: 'collection_history',
    columns: {
      id: 'id',
      collectionId: 'collection_id',
      collectionStatusId: 'collection_status_id',
      executionDate: 'execution_date',
      openDate: 'open_date',
      closeDate: 'close_date'
    }
  },

  // Collection Institute History table
  CollectionInstituteHistoryTable: {
    tableName: 'collection_institute_history',
    columns: {
      id: 'id',
      collection_id: 'collection_id',
      collection_history_id: 'collection_history_id',
      institute_id: 'institute_id',
      form_id: 'form_id',
      template_form_id: 'template_form_id',
      collection_status_id: 'collection_status_id',
      execution_date: 'execution_date',
      manual_entry: 'manual_entry'
    }
  },

  // Users table
  UsersTable: {
    tableName: 'users',
    columns: {
      id: 'id',
      cognito_sub: 'cognito_sub',
      name: 'name',
      email: 'email',
      created_at: 'created_at',
      updated_at: 'updated_at',
      institute_id: 'institute_id',
      enabled: 'enabled'
    }
  }
};

// For backward compatibility
export const InstituteTable = Tables.InstituteTable;

/**
 * Database schema interface for Kysely
 *
 * This interface defines the structure of our database tables.
 * Each property represents a table, and the value is the table's row type.
 */
export default {
  // Institute table
  institutes: {
    id: 'number',
    name: 'string',
    logo: 'string',
    contact_number: 'string | null',
    email: 'string | null'
  },

  // Collection Status table
  collection_status: {
    id: 'number',
    name: 'string',
    description: 'string | null'
  },

  // Collection Type table
  collection_type: {
    id: 'number',
    name: 'string',
    description: 'string | null'
  },

  // Form table
  form: {
    id: 'number',
    name: 'string',
    description: 'string | null',
    is_template: 'boolean | null',
    file_name: 'string | null',
    manual_entry_mappings: 'object | null',
    template_form_mappings: 'object | null'
  },

  // Frequency table
  frequency: {
    id: 'number',
    name: 'string',
    description: 'string | null'
  },

  // Collection Window table
  collection_window: {
    id: 'number',
    name: 'string',
    description: 'string | null'
  },

  // Data Collection table
  data_collection: {
    id: 'number',
    name: 'string | null',
    description: 'string | null',
    collection_type_id: 'number | null',
    collection_window_id: 'number | null',
    email_frequency_id: 'number | null',
    warning_email: 'boolean | null',
    warning_email_threshold: 'number | null',
    warning_email_message: 'string | null',
    reminder_emails: 'boolean | null'
  },

  // Collection Institute table (junction table)
  collection_institute: {
    collection_id: 'number',
    institute_id: 'number'
  },

  // Collection Form table (junction table)
  collection_form: {
    collection_id: 'number',
    form_id: 'number'
  },

  // Collection History table
  collection_history: {
    id: 'number',
    collection_id: 'number',
    collection_status_id: 'number',
    execution_date: 'Date',
    open_date: 'Date | null',
    close_date: 'Date | null'
  },

  // Collection Institute History table
  collection_institute_history: {
    id: 'number',
    collection_id: 'number',
    collection_history_id: 'number',
    institute_id: 'number',
    form_id: 'number',
    template_form_id: 'number',
    collection_status_id: 'number',
    execution_date: 'Date',
    manual_entry: 'object | null'
  },

  // Users table
  users: {
    id: 'number',
    cognito_sub: 'string',
    name: 'string',
    email: 'string',
    created_at: 'Date',
    updated_at: 'Date',
    institute_id: 'number',
    enabled: 'boolean'
  }
};
