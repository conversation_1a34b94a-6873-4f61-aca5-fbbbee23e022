/**
 * Database operations for data collection tables
 */
import { Tables } from './schema.js';
import { sql } from 'kysely';
import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

// Initialize S3 client
const s3Client = new S3Client({ region: process.env.AWS_REGION || 'us-east-1' });

/**
 * Get the content type based on file extension
 * @param {string} filename - The name of the file
 * @returns {string} The MIME type for the file
 */
const getContentType = (filename) => {
  const parts = filename.toLowerCase().split('.');
  if (parts.length < 2) {
    return 'application/octet-stream';
  }
  const extension = parts[parts.length - 1];
  switch (extension) {
    case 'pdf':
      return 'application/pdf';
    case 'xlsx':
      return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    case 'xls':
      return 'application/vnd.ms-excel';
    case 'csv':
      return 'text/csv';
    default:
      return 'application/octet-stream';
  }
};

/**
 * Generate a presigned URL for a form file
 * @param {number} formId - The form ID
 * @param {string} fileName - The file name
 * @returns {Promise<string|null>} Presigned URL or null if generation fails
 */
const generateFormPresignedUrl = async (formId, fileName) => {
  try {
    // Create the file key using the new format: {id}/{file_name}
    const fileKey = `${formId}/${fileName}`;

    const command = new GetObjectCommand({
      Bucket: process.env.FORMS_BUCKET_NAME,
      Key: fileKey,
      ResponseContentType: getContentType(fileName)
    });

    // Generate presigned URL that expires in 1 hour
    const presignedUrl = await getSignedUrl(s3Client, command, { expiresIn: 3600 });
    return presignedUrl;
  } catch (error) {
    console.error('Error generating presigned URL:', error);
    return null;
  }
};

/**
 * Create database operations for the data collection tables
 *
 * @param {Kysely} db - Kysely database client instance
 * @returns {Object} - Object containing database operations
 */
export const collectionOperations = (db) => ({
  /**
   * Collection Status Operations
   */
  collectionStatus: {
    /**
     * Get all collection statuses
     * @returns {Promise<Array>} Array of collection status objects
     */
    getAll: async () => {
      return await db
        .selectFrom(Tables.CollectionStatusTable.tableName)
        .selectAll()
        .execute();
    },

    /**
     * Get a collection status by ID
     * @param {number} id - The collection status ID
     * @returns {Promise<Object|null>} Collection status object or null if not found
     */
    getById: async (id) => {
      return await db
        .selectFrom(Tables.CollectionStatusTable.tableName)
        .selectAll()
        .where('id', '=', id)
        .executeTakeFirst();
    },

    /**
     * Create a new collection status
     * @param {Object} status - The collection status object to create
     * @returns {Promise<Object>} Created collection status object
     */
    create: async (status) => {
      return await db
        .insertInto(Tables.CollectionStatusTable.tableName)
        .values({
          name: status.name,
          description: status.description || null
        })
        .returning(['id', 'name', 'description'])
        .executeTakeFirst();
    },

    /**
     * Update a collection status
     * @param {number} id - The collection status ID
     * @param {Object} status - The collection status object to update
     * @returns {Promise<Object>} Updated collection status object
     */
    update: async (id, status) => {
      return await db
        .updateTable(Tables.CollectionStatusTable.tableName)
        .set({
          name: status.name,
          description: status.description || null
        })
        .where('id', '=', id)
        .returning(['id', 'name', 'description'])
        .executeTakeFirst();
    },

    /**
     * Delete a collection status
     * @param {number} id - The collection status ID
     * @returns {Promise<boolean>} True if deleted, false if not found
     */
    delete: async (id) => {
      const result = await db
        .deleteFrom(Tables.CollectionStatusTable.tableName)
        .where('id', '=', id)
        .execute();

      return result.length > 0;
    }
  },

  /**
   * Collection Type Operations
   */
  collectionType: {
    /**
     * Get all collection types
     * @returns {Promise<Array>} Array of collection type objects
     */
    getAll: async () => {
      return await db
        .selectFrom(Tables.CollectionTypeTable.tableName)
        .selectAll()
        .execute();
    },

    /**
     * Get a collection type by ID
     * @param {number} id - The collection type ID
     * @returns {Promise<Object|null>} Collection type object or null if not found
     */
    getById: async (id) => {
      return await db
        .selectFrom(Tables.CollectionTypeTable.tableName)
        .selectAll()
        .where('id', '=', id)
        .executeTakeFirst();
    },

    /**
     * Create a new collection type
     * @param {Object} type - The collection type object to create
     * @returns {Promise<Object>} Created collection type object
     */
    create: async (type) => {
      return await db
        .insertInto(Tables.CollectionTypeTable.tableName)
        .values({
          name: type.name,
          description: type.description || null
        })
        .returning(['id', 'name', 'description'])
        .executeTakeFirst();
    },

    /**
     * Update a collection type
     * @param {number} id - The collection type ID
     * @param {Object} type - The collection type object to update
     * @returns {Promise<Object>} Updated collection type object
     */
    update: async (id, type) => {
      return await db
        .updateTable(Tables.CollectionTypeTable.tableName)
        .set({
          name: type.name,
          description: type.description || null
        })
        .where('id', '=', id)
        .returning(['id', 'name', 'description'])
        .executeTakeFirst();
    },

    /**
     * Delete a collection type
     * @param {number} id - The collection type ID
     * @returns {Promise<boolean>} True if deleted, false if not found
     */
    delete: async (id) => {
      const result = await db
        .deleteFrom(Tables.CollectionTypeTable.tableName)
        .where('id', '=', id)
        .execute();

      return result.length > 0;
    }
  },

  /**
   * Form Operations
   */
  form: {
    /**
     * Get all forms
     * @returns {Promise<Array>} Array of form objects with presigned URLs for templates
     */
    getAll: async () => {
      const forms = await db
        .selectFrom(Tables.FormTable.tableName)
        .selectAll()
        .execute();

      // Generate presigned URLs for template forms
      const formsWithUrls = await Promise.all(
        forms.map(async (form) => {
          if (form.is_template && form.file_name) {
            form.download_url = await generateFormPresignedUrl(form.id, form.file_name);
          }
          return form;
        })
      );

      return formsWithUrls;
    },

    /**
     * Get a form by ID
     * @param {number} id - The form ID
     * @returns {Promise<Object|null>} Form object with presigned URL or null if not found
     */
    getById: async (id) => {
      const form = await db
        .selectFrom(Tables.FormTable.tableName)
        .selectAll()
        .where('id', '=', id)
        .executeTakeFirst();

      if (!form) return null;

      // Generate presigned URL for the form if it has a file name
      if (form.file_name) {
        form.download_url = await generateFormPresignedUrl(form.id, form.file_name);
      }

      return form;
    },

    /**
     * Get a form by name
     * @param {string} name - The form name
     * @returns {Promise<Object|null>} Form object with presigned URL or null if not found
     */
    getByName: async (name) => {
      const form = await db
        .selectFrom(Tables.FormTable.tableName)
        .selectAll()
        .where('name', '=', name)
        .executeTakeFirst();

      if (!form) return null;

      // Generate presigned URL for the form if it has a file name
      if (form.file_name) {
        form.download_url = await generateFormPresignedUrl(form.id, form.file_name);
      }

      return form;
    },

    /**
     * Create a new form
     * @param {Object} form - The form object to create
     * @returns {Promise<Object>} Created form object
     */
    create: async (form) => {
      return await db
        .insertInto(Tables.FormTable.tableName)
        .values({
          name: form.name,
          description: form.description || null,
          is_template: form.is_template || false,
          file_name: form.file_name || null,
          manual_entry_mappings: form.manual_entry_mappings || null,
          template_form_mappings: form.template_form_mappings || null
        })
        .returning(['id', 'name', 'description', 'is_template', 'file_name', 'manual_entry_mappings', 'template_form_mappings'])
        .executeTakeFirst();
    },

    /**
     * Update a form
     * @param {number} id - The form ID
     * @param {Object} form - The form object to update
     * @returns {Promise<Object>} Updated form object
     */
    update: async (id, form) => {
      return await db
        .updateTable(Tables.FormTable.tableName)
        .set({
          name: form.name,
          description: form.description || null,
          is_template: form.is_template || false,
          file_name: form.file_name || null,
          manual_entry_mappings: form.manual_entry_mappings || null,
          template_form_mappings: form.template_form_mappings || null
        })
        .where('id', '=', id)
        .returning(['id', 'name', 'description', 'is_template', 'file_name', 'manual_entry_mappings', 'template_form_mappings'])
        .executeTakeFirst();
    },

    /**
     * Delete a form
     * @param {number} id - The form ID
     * @returns {Promise<boolean>} True if deleted, false if not found
     */
    delete: async (id) => {
      const result = await db
        .deleteFrom(Tables.FormTable.tableName)
        .where('id', '=', id)
        .execute();

      return result.length > 0;
    },

    generateFormPresignedUrl,
  },

  /**
   * Collection Window Operations
   */
  collectionWindow: {
    /**
     * Get all collection windows
     * @returns {Promise<Array>} Array of collection window objects
     */
    getAll: async () => {
      return await db
        .selectFrom(Tables.CollectionWindowTable.tableName)
        .selectAll()
        .execute();
    },

    /**
     * Get a collection window by ID
     * @param {number} id - The collection window ID
     * @returns {Promise<Object|null>} Collection window object or null if not found
     */
    getById: async (id) => {
      return await db
        .selectFrom(Tables.CollectionWindowTable.tableName)
        .selectAll()
        .where('id', '=', id)
        .executeTakeFirst();
    },

    /**
     * Create a new collection window
     * @param {Object} window - The collection window object to create
     * @returns {Promise<Object>} Created collection window object
     */
    create: async (window) => {
      return await db
        .insertInto(Tables.CollectionWindowTable.tableName)
        .values({
          name: window.name,
          description: window.description || null
        })
        .returning(['id', 'name', 'description'])
        .executeTakeFirst();
    },

    /**
     * Update a collection window
     * @param {number} id - The collection window ID
     * @param {Object} window - The collection window object to update
     * @returns {Promise<Object>} Updated collection window object
     */
    update: async (id, window) => {
      return await db
        .updateTable(Tables.CollectionWindowTable.tableName)
        .set({
          name: window.name,
          description: window.description || null
        })
        .where('id', '=', id)
        .returning(['id', 'name', 'description'])
        .executeTakeFirst();
    },

    /**
     * Delete a collection window
     * @param {number} id - The collection window ID
     * @returns {Promise<boolean>} True if deleted, false if not found
     */
    delete: async (id) => {
      const result = await db
        .deleteFrom(Tables.CollectionWindowTable.tableName)
        .where('id', '=', id)
        .execute();

      return result.length > 0;
    }
  },

  /**
   * Frequency Operations
   */
  frequency: {
    /**
     * Get all frequencies
     * @returns {Promise<Array>} Array of frequency objects
     */
    getAll: async () => {
      return await db
        .selectFrom(Tables.FrequencyTable.tableName)
        .selectAll()
        .execute();
    },

    /**
     * Get a frequency by ID
     * @param {number} id - The frequency ID
     * @returns {Promise<Object|null>} Frequency object or null if not found
     */
    getById: async (id) => {
      return await db
        .selectFrom(Tables.FrequencyTable.tableName)
        .selectAll()
        .where('id', '=', id)
        .executeTakeFirst();
    },

    /**
     * Create a new frequency
     * @param {Object} frequency - The frequency object to create
     * @returns {Promise<Object>} Created frequency object
     */
    create: async (frequency) => {
      return await db
        .insertInto(Tables.FrequencyTable.tableName)
        .values({
          name: frequency.name,
          description: frequency.description || null
        })
        .returning(['id', 'name', 'description'])
        .executeTakeFirst();
    },

    /**
     * Update a frequency
     * @param {number} id - The frequency ID
     * @param {Object} frequency - The frequency object to update
     * @returns {Promise<Object>} Updated frequency object
     */
    update: async (id, frequency) => {
      return await db
        .updateTable(Tables.FrequencyTable.tableName)
        .set({
          name: frequency.name,
          description: frequency.description || null
        })
        .where('id', '=', id)
        .returning(['id', 'name', 'description'])
        .executeTakeFirst();
    },

    /**
     * Delete a frequency
     * @param {number} id - The frequency ID
     * @returns {Promise<boolean>} True if deleted, false if not found
     */
    delete: async (id) => {
      const result = await db
        .deleteFrom(Tables.FrequencyTable.tableName)
        .where('id', '=', id)
        .execute();

      return result.length > 0;
    }
  },

  /**
   * Data Collection Operations
   */
  dataCollection: {
    /**
     * Get all data collections
     * @returns {Promise<Array>} Array of data collection objects
     */
    getAll: async () => {
      return await db
        .selectFrom(Tables.DataCollectionTable.tableName)
        .selectAll()
        .execute();
    },

    /**
     * Get paginated data collections with optional filters
     * @param {number} offset - Number of records to skip
     * @param {number} limit - Maximum number of records to return
     * @param {Object} filters - Optional filters for the query
     * @param {number} [filters.typeId] - Filter by collection type ID
     * @param {number} [filters.statusId] - Filter by collection status ID
     * @param {number} [filters.windowId] - Filter by collection window ID
     * @returns {Promise<Array>} Array of data collection objects
     */
    getPaginated: async (offset, limit, filters = {}) => {
      let query = db
        .selectFrom(Tables.DataCollectionTable.tableName)
        .selectAll();

      // Apply filters if provided
      if (filters.typeId) {
        query = query.where('collection_type_id', '=', filters.typeId);
      }

      if (filters.statusId) {
        query = query.where('collection_status_id', '=', filters.statusId);
      }

      if (filters.windowId) {
        query = query.where('collection_window_id', '=', filters.windowId);
      }

      return await query
        .offset(offset)
        .limit(limit)
        .execute();
    },

    /**
     * Get total count of data collections with optional filters
     * @param {Object} filters - Optional filters for the query
     * @param {number} [filters.typeId] - Filter by collection type ID
     * @param {number} [filters.statusId] - Filter by collection status ID
     * @param {number} [filters.windowId] - Filter by collection window ID
     * @returns {Promise<number>} Total count of data collections
     */
    getTotalCount: async (filters = {}) => {
      let query = db
        .selectFrom(Tables.DataCollectionTable.tableName)
        .select(db.fn.count('id').as('count'));

      // Apply filters if provided
      if (filters.typeId) {
        query = query.where('collection_type_id', '=', filters.typeId);
      }

      if (filters.statusId) {
        query = query.where('collection_status_id', '=', filters.statusId);
      }

      if (filters.windowId) {
        query = query.where('collection_window_id', '=', filters.windowId);
      }

      const result = await query.executeTakeFirst();

      return parseInt(result.count, 10);
    },

    /**
     * Get a data collection by ID
     * @param {number} id - The data collection ID
     * @returns {Promise<Object|null>} Data collection object or null if not found
     */
    getById: async (id) => {
      return await db
        .selectFrom(Tables.DataCollectionTable.tableName)
        .selectAll()
        .where('id', '=', id)
        .executeTakeFirst();
    },

    /**
     * Create a new data collection
     * @param {Object} collection - The data collection object to create
     * @param {string} collection.name - The name of the collection
     * @param {string} [collection.description] - The description of the collection
     * @param {Date} [collection.open_date] - The open date of the collection
     * @param {Date} [collection.close_date] - The close date of the collection
     * @param {number} [collection.collection_type_id] - The collection type ID
     * @param {number} [collection.collection_window_id] - The collection window ID
     * @param {number} [collection.email_frequency_id] - The email frequency ID
     * @param {boolean} [collection.warning_email] - Whether warning emails are enabled
     * @param {number} [collection.warning_email_threshold] - The warning email threshold
     * @param {string} [collection.warning_email_message] - The warning email message
     * @param {boolean} [collection.reminder_emails] - Whether reminder emails are enabled
     * @returns {Promise<Object>} Created data collection object
     */
    create: async (collection) => {
      return await db
        .insertInto(Tables.DataCollectionTable.tableName)
        .values({
          name: collection.name || null,
          description: collection.description || null,
          collection_type_id: collection.collection_type_id || null,
          collection_window_id: collection.collection_window_id || null,
          email_frequency_id: collection.email_frequency_id || null,
          warning_email: collection.warning_email || false,
          warning_email_threshold: collection.warning_email_threshold || null,
          warning_email_message: collection.warning_email_message || null,
          reminder_emails: collection.reminder_emails || false
        })
        .returning([
          'id',
          'name',
          'description',
          'collection_type_id',
          'collection_window_id',
          'email_frequency_id',
          'warning_email',
          'warning_email_threshold',
          'warning_email_message',
          'reminder_emails'
        ])
        .executeTakeFirst();
    },

    /**
     * Update a data collection
     * @param {number} id - The data collection ID
     * @param {Object} collection - The data collection object to update
     * @returns {Promise<Object>} Updated data collection object
     */
    update: async (id, collection) => {
      const updateFields = {};
      if (collection.name !== undefined && collection.name !== null) updateFields.name = collection.name;
      if (collection.description !== undefined && collection.description !== null) updateFields.description = collection.description;
      if (collection.collection_type_id !== undefined && collection.collection_type_id !== null) updateFields.collection_type_id = collection.collection_type_id;
      if (collection.collection_window_id !== undefined && collection.collection_window_id !== null) updateFields.collection_window_id = collection.collection_window_id;
      if (collection.email_frequency_id !== undefined && collection.email_frequency_id !== null) updateFields.email_frequency_id = collection.email_frequency_id;
      if (collection.warning_email !== undefined && collection.warning_email !== null) updateFields.warning_email = collection.warning_email;
      if (collection.warning_email_threshold !== undefined && collection.warning_email_threshold !== null) updateFields.warning_email_threshold = collection.warning_email_threshold;
      if (collection.warning_email_message !== undefined && collection.warning_email_message !== null) updateFields.warning_email_message = collection.warning_email_message;
      if (collection.reminder_emails !== undefined && collection.reminder_emails !== null) updateFields.reminder_emails = collection.reminder_emails;
      if (collection.last_reminder !== undefined && collection.last_reminder !== null) updateFields.last_reminder = collection.last_reminder;
      return await db
        .updateTable(Tables.DataCollectionTable.tableName)
        .set(updateFields)
        .where('id', '=', id)
        .returning([
          'id',
          'name',
          'description',
          'collection_type_id',
          'collection_window_id',
          'email_frequency_id',
          'warning_email',
          'warning_email_threshold',
          'warning_email_message',
          'reminder_emails',
          'last_reminder'
        ])
        .executeTakeFirst();
    },

    /**
     * Delete a data collection
     * @param {number} id - The data collection ID
     * @returns {Promise<boolean>} True if deleted, false if not found
     */
    delete: async (id) => {
      const result = await db
        .deleteFrom(Tables.DataCollectionTable.tableName)
        .where('id', '=', id)
        .execute();

      return result.length > 0;
    },

    /**
     * Get forms for a data collection
     * @param {number} collectionId - The data collection ID
     * @returns {Promise<Array>} Array of form objects with presigned URLs for templates
     */
    getForms: async (collectionId) => {
      const forms = await db
        .selectFrom(Tables.CollectionFormTable.tableName)
        .innerJoin(
          Tables.FormTable.tableName,
          `${Tables.CollectionFormTable.tableName}.form_id`,
          `${Tables.FormTable.tableName}.id`
        )
        .select([
          `${Tables.FormTable.tableName}.id`,
          `${Tables.FormTable.tableName}.name`,
          `${Tables.FormTable.tableName}.description`,
          `${Tables.FormTable.tableName}.is_template`,
          `${Tables.FormTable.tableName}.file_name`
        ])
        .where(`${Tables.CollectionFormTable.tableName}.collection_id`, '=', collectionId)
        .execute();

      // Generate presigned URLs for template forms
      const formsWithUrls = await Promise.all(
        forms.map(async (form) => {
          if (form.is_template && form.file_name) {
            form.download_url = await generateFormPresignedUrl(form.id, form.file_name);
          }
          return form;
        })
      );

      return formsWithUrls;
    },

    /**
     * Add a form to a data collection
     * @param {number} collectionId - The data collection ID
     * @param {number} formId - The form ID
     * @returns {Promise<Object>} Created collection form object
     */
    addForm: async (collectionId, formId) => {
      return await db
        .insertInto(Tables.CollectionFormTable.tableName)
        .values({
          collection_id: collectionId,
          form_id: formId
        })
        .returning(['collection_id', 'form_id'])
        .executeTakeFirst();
    },

    /**
     * Remove a form from a data collection
     * @param {number} collectionId - The data collection ID
     * @param {number} formId - The form ID
     * @returns {Promise<boolean>} True if removed, false if not found
     */
    removeForm: async (collectionId, formId) => {
      const result = await db
        .deleteFrom(Tables.CollectionFormTable.tableName)
        .where('collection_id', '=', collectionId)
        .where('form_id', '=', formId)
        .execute();

      return result.length > 0;
    },

    /**
     * Get institutes for a data collection
     * @param {number} collectionId - The data collection ID
     * @returns {Promise<Array>} Array of institute objects
     */
    getInstitutes: async (collectionId) => {
      return await db
        .selectFrom(Tables.CollectionInstituteTable.tableName)
        .innerJoin(
          'institutes',
          `${Tables.CollectionInstituteTable.tableName}.institute_id`,
          'institutes.id'
        )
        .select([
          'institutes.id',
          'institutes.name',
          'institutes.email',
          'institutes.contact_number'
        ])
        .where(`${Tables.CollectionInstituteTable.tableName}.collection_id`, '=', collectionId)
        .execute();
    },

    /**
     * Add an institute to a data collection
     * @param {number} collectionId - The data collection ID
     * @param {number} instituteId - The institute ID
     * @returns {Promise<Object>} Created collection institute object
     */
    addInstitute: async (collectionId, instituteId) => {
      return await db
        .insertInto(Tables.CollectionInstituteTable.tableName)
        .values({
          collection_id: collectionId,
          institute_id: instituteId
        })
        .returning(['collection_id', 'institute_id'])
        .executeTakeFirst();
    },

    /**
     * Remove an institute from a data collection
     * @param {number} collectionId - The data collection ID
     * @param {number} instituteId - The institute ID
     * @returns {Promise<boolean>} True if removed, false if not found
     */
    removeInstitute: async (collectionId, instituteId) => {
      const result = await db
        .deleteFrom(Tables.CollectionInstituteTable.tableName)
        .where('collection_id', '=', collectionId)
        .where('institute_id', '=', instituteId)
        .execute();

      return result.length > 0;
    }
  },

  /**
   * Collection History Operations
   */
  collectionHistory: {
    /**
     * Get all collection history entries
     * @returns {Promise<Array>} Array of collection history objects
     */
    getAll: async () => {
      return await db
        .selectFrom(Tables.CollectionHistoryTable.tableName)
        .selectAll()
        .execute();
    },

    /**
     * Get collection history entries by collection ID
     * @param {number} collectionId - The collection ID
     * @param {number} [historyId] - Optional history ID to filter by
     * @returns {Promise<Array>} Array of collection history objects
     */
    getByCollectionId: async (collectionId, historyId) => {
      let query = db
        .selectFrom(Tables.CollectionHistoryTable.tableName)
        .selectAll()
        .where('collection_id', '=', collectionId);

      if (historyId) {
        query = query.where('id', '=', historyId);
      }

      return await query.execute();
    },

    /**
     * Get paginated collection history entries with filters and sorting
     * @param {number} offset - The offset for pagination
     * @param {number} limit - The limit for pagination
     * @param {Object} filters - The filters to apply
     * @param {string} [sortKey='executionDate'] - The field to sort by
     * @param {string} [sortDirection='desc'] - The sort direction ('asc' or 'desc')
     * @returns {Promise<Array>} Array of collection history objects
     */
    getPaginated: async (offset, limit, filters = {}, sortKey = 'executionDate', sortDirection = 'desc') => {

      let query = db
        .selectFrom(Tables.CollectionHistoryTable.tableName + ' as ch')
        .leftJoin(Tables.DataCollectionTable.tableName + ' as dc', 'ch.collection_id', 'dc.id')
        .leftJoin(Tables.CollectionInstituteTable.tableName + ' as ci', 'dc.id', 'ci.collection_id')
        .select([
          'ch.id',
          'ch.collection_id',
          'ch.collection_status_id',
          'ch.execution_date',
          'dc.name as collection_name',
          'dc.description',
          'ch.open_date',
          'ch.close_date',
          'dc.collection_type_id',
          'dc.collection_window_id',
          'dc.email_frequency_id',
          'dc.warning_email',
          'dc.warning_email_threshold',
          'dc.warning_email_message',
          'dc.reminder_emails',
          'dc.last_reminder'
        ])
        .distinctOn('ch.id')
        .orderBy('ch.id');

      // Apply filters
      if (filters.collectionId) {
        if (Array.isArray(filters.collectionId)) {
          query = query.where('ch.collection_id', 'in', filters.collectionId);
        } else {
          query = query.where('ch.collection_id', '=', filters.collectionId);
        }
      }

      if (filters.statusId) {
        if (Array.isArray(filters.statusId)) {
          query = query.where('ch.collection_status_id', 'in', filters.statusId);
        } else {
          query = query.where('ch.collection_status_id', '=', filters.statusId);
        }
      }

      if (filters.year) {
        query = query.where(sql`EXTRACT(YEAR FROM ch.execution_date)`, '=', filters.year);
      }

      if (filters.instituteId) {
        if (Array.isArray(filters.instituteId)) {
          query = query.where('ci.institute_id', 'in', filters.instituteId);
        } else {
          query = query.where('ci.institute_id', '=', filters.instituteId);
        }
      }

      if (filters.windowId) {
        if (Array.isArray(filters.windowId)) {
          query = query.where('dc.collection_window_id', 'in', filters.windowId);
        } else {
          query = query.where('dc.collection_window_id', '=', filters.windowId);
        }
      }

      if (filters.typeId) {
        if (Array.isArray(filters.typeId)) {
          query = query.where('dc.collection_type_id', 'in', filters.typeId);
        } else {
          query = query.where('dc.collection_type_id', '=', filters.typeId);
        }
      }

      // Open date filters
      if (filters.openDateFrom) {
        query = query.where('ch.open_date', '>=', filters.openDateFrom);
      }

      if (filters.openDateTo) {
        query = query.where('ch.open_date', '<=', filters.openDateTo);
      }

      // Close date filters
      if (filters.closeDateFrom) {
        query = query.where('ch.close_date', '>=', filters.closeDateFrom);
      }

      if (filters.closeDateTo) {
        query = query.where('ch.close_date', '<=', filters.closeDateTo);
      }

      // Name filter for collection name
      if (filters.name) {
        query = query.where('dc.name', 'ilike', `%${filters.name}%`);
      }

      // Group by all selected columns to avoid duplicates from joins
      query = query.groupBy([
        'ch.id',
        'ch.collection_id',
        'ch.collection_status_id',
        'ch.execution_date',
        'dc.name',
        'dc.description',
        'ch.open_date',
        'ch.close_date',
        'dc.collection_type_id',
        'dc.collection_window_id',
        'dc.email_frequency_id',
        'dc.warning_email',
        'dc.warning_email_threshold',
        'dc.warning_email_message',
        'dc.reminder_emails',
        'dc.last_reminder'
      ]);

      // Map sortKey to the appropriate column with table alias
      let sortColumn;
      switch (sortKey) {
        case 'executionDate':
          sortColumn = 'ch.execution_date';
          break;
        case 'collection':
          sortColumn = 'ch.collection_id';
          break;
        case 'status':
          sortColumn = 'ch.collection_status_id';
          break;
        case 'open_date':
          sortColumn = 'ch.open_date';
          break;
        case 'close_date':
          sortColumn = 'ch.close_date';
          break;
        default:
          sortColumn = 'ch.execution_date';
      }

      // Apply sorting (after ch.id)
      if (sortDirection === 'asc') {
        query = query.orderBy(sortColumn, 'asc');
      } else {
        query = query.orderBy(sortColumn, 'desc');
      }

      // Apply pagination
      return await query
        .offset(offset)
        .limit(limit)
        .execute();
    },

    /**
     * Get total count of collection history entries with filters
     * @param {Object} filters - The filters to apply
     * @returns {Promise<number>} Total count
     */
    getTotalCount: async (filters = {}) => {
      // Use a subquery approach to count distinct history IDs
      let subquery = db
        .selectFrom(Tables.CollectionHistoryTable.tableName + ' as ch')
        .leftJoin(Tables.DataCollectionTable.tableName + ' as dc', 'ch.collection_id', 'dc.id')
        .leftJoin(Tables.CollectionInstituteTable.tableName + ' as ci', 'dc.id', 'ci.collection_id')
        .select('ch.id');

      // Apply filters to the subquery
      if (filters.collectionId) {
        if (Array.isArray(filters.collectionId)) {
          subquery = subquery.where('ch.collection_id', 'in', filters.collectionId);
        } else {
          subquery = subquery.where('ch.collection_id', '=', filters.collectionId);
        }
      }

      if (filters.statusId) {
        if (Array.isArray(filters.statusId)) {
          subquery = subquery.where('ch.collection_status_id', 'in', filters.statusId);
        } else {
          subquery = subquery.where('ch.collection_status_id', '=', filters.statusId);
        }
      }

      if (filters.year) {
        subquery = subquery.where(sql`EXTRACT(YEAR FROM ch.execution_date)`, '=', filters.year);
      }

      if (filters.instituteId) {
        if (Array.isArray(filters.instituteId)) {
          subquery = subquery.where('ci.institute_id', 'in', filters.instituteId);
        } else {
          subquery = subquery.where('ci.institute_id', '=', filters.instituteId);
        }
      }

      if (filters.windowId) {
        if (Array.isArray(filters.windowId)) {
          subquery = subquery.where('dc.collection_window_id', 'in', filters.windowId);
        } else {
          subquery = subquery.where('dc.collection_window_id', '=', filters.windowId);
        }
      }

      if (filters.typeId) {
        if (Array.isArray(filters.typeId)) {
          subquery = subquery.where('dc.collection_type_id', 'in', filters.typeId);
        } else {
          subquery = subquery.where('dc.collection_type_id', '=', filters.typeId);
        }
      }

      // Open date filters
      if (filters.openDateFrom) {
        subquery = subquery.where('ch.open_date', '>=', filters.openDateFrom);
      }

      if (filters.openDateTo) {
        subquery = subquery.where('ch.open_date', '<=', filters.openDateTo);
      }

      // Close date filters
      if (filters.closeDateFrom) {
        subquery = subquery.where('ch.close_date', '>=', filters.closeDateFrom);
      }

      if (filters.closeDateTo) {
        subquery = subquery.where('ch.close_date', '<=', filters.closeDateTo);
      }

      // Name filter for collection name
      if (filters.name) {
        subquery = subquery.where('dc.name', 'ilike', `%${filters.name}%`);
      }

      // Execute the subquery as a CTE and count the results
      const result = await db
        .with('filtered_histories', () => subquery)
        .selectFrom('filtered_histories')
        .select(({ fn, ref }) => fn.count(sql`DISTINCT ${ref('id')}`).as('count'))
        .executeTakeFirst();
      return parseInt(result.count, 10);
    },

    /**
     * Create a new collection history entry
     * @param {Object} history - The collection history object to create
     * @param {number} history.collection_id - The collection ID
     * @param {number} history.collection_status_id - The collection status ID
     * @param {Date} [history.execution_date] - The execution date (defaults to current date/time)
     * @param {Date} [history.open_date] - The open date (optional)
     * @param {Date} [history.close_date] - The close date (optional)
     * @returns {Promise<Object>} Created collection history object
     */
    create: async (history) => {
      return await db
        .insertInto(Tables.CollectionHistoryTable.tableName)
        .values({
          collection_id: history.collection_id,
          collection_status_id: history.collection_status_id,
          execution_date: history.execution_date || new Date(),
          open_date: history.open_date || null,
          close_date: history.close_date || null
        })
        .returning(['id', 'collection_id', 'collection_status_id', 'execution_date', 'open_date', 'close_date'])
        .executeTakeFirst();
    },

    /**
     * Update a collection history entry
     * @param {number} id - The collection history ID
     * @param {Object} history - The collection history object to update
     * @param {number} [history.collection_id] - The collection ID
     * @param {number} [history.collection_status_id] - The collection status ID
     * @param {Date} [history.execution_date] - The execution date
     * @param {Date} [history.open_date] - The open date (optional)
     * @param {Date} [history.close_date] - The close date (optional)
     * @returns {Promise<Object>} Updated collection history object
     */
    update: async (id, history) => {
      const updateFields = {};
      
      if (history.collection_id !== undefined && history.collection_id !== null) updateFields.collection_id = history.collection_id;
      if (history.collection_status_id !== undefined && history.collection_status_id !== null) updateFields.collection_status_id = history.collection_status_id;
      if (history.execution_date !== undefined && history.execution_date !== null) updateFields.execution_date = history.execution_date;
      if (history.open_date !== undefined && history.open_date !== null) updateFields.open_date = history.open_date;
      if (history.close_date !== undefined) updateFields.close_date = history.close_date; // Allow null for close_date (adhoc collections)

      return await db
        .updateTable(Tables.CollectionHistoryTable.tableName)
        .set(updateFields)
        .where('id', '=', id)
        .returning(['id', 'collection_id', 'collection_status_id', 'execution_date', 'open_date', 'close_date'])
        .executeTakeFirst();
    },

    /**
     * Get a collection_history record by its id
     * @param {string|number} id
     * @returns {Promise<Object|null>}
     */
    getById: async (id) => {
      return await db
        .selectFrom(Tables.CollectionHistoryTable.tableName)
        .selectAll()
        .where('id', '=', id)
        .executeTakeFirst();
    }
  },

  collectionInstituteHistory: {
    /**
     * Create a new collection institute history record
     * @param {Object} data - The collection institute history data
     * @param {number} data.collection_history_id - The collection history ID
     * @param {number} data.institute_id - The institute ID
     * @param {number} data.form_id - The form ID
     * @param {number} [data.template_form_id] - The template form ID
     * @param {number} data.collection_status_id - The collection status ID
     * @param {number} [data.prev_collection_status_id] - The previous collection status ID
     * @param {Date} [data.execution_date] - The execution date
     * @param {boolean} [data.manual_entry] - Whether the record is a manual entry
     * @returns {Promise<Object>} The created record
     */
    create: async (data) => {
      return await db
        .insertInto(Tables.CollectionInstituteHistoryTable.tableName)
        .values({
          collection_history_id: data.collection_history_id,
          institute_id: data.institute_id,
          form_id: data.form_id,
          template_form_id: data.template_form_id || null,
          collection_status_id: data.collection_status_id,
          prev_collection_status_id: data.prev_collection_status_id || null,
          execution_date: data.execution_date || new Date(),
          manual_entry: data.manual_entry || null
        })
        .returning(['id', 'collection_history_id', 'institute_id', 'form_id', 'template_form_id', 'collection_status_id', 'prev_collection_status_id', 'execution_date', 'manual_entry'])
        .executeTakeFirst();
    },

    /**
     * Get all collection institute history records
     * @returns {Promise<Array>} Array of records
     */
    getAll: async () => {
      return await db
        .selectFrom(Tables.CollectionInstituteHistoryTable.tableName)
        .selectAll()
        .execute();
    },

    /**
     * Get a collection institute history record by ID
     * @param {number} id - The record ID
     * @returns {Promise<Object>} The record
     */
    getById: async (id) => {
      return await db
        .selectFrom(Tables.CollectionInstituteHistoryTable.tableName)
        .selectAll()
        .where('id', '=', id)
        .executeTakeFirst();
    },

    /**
     * Get collection institute history records by institute ID
     * @param {number} instituteId - The institute ID
     * @returns {Promise<Array>} Array of records
     */
    getByInstituteId: async (instituteId) => {
      return await db
        .selectFrom(Tables.CollectionInstituteHistoryTable.tableName)
        .selectAll()
        .where('institute_id', '=', instituteId)
        .execute();
    },

    /**
     * Get collection institute history records by collection history ID
     * @param {number} collectionHistoryId - The collection history ID
     * @returns {Promise<Array>} Array of records
     */
    getByCollectionHistoryId: async (collectionHistoryId) => {
      return await db
        .selectFrom(Tables.CollectionInstituteHistoryTable.tableName)
        .selectAll()
        .where('collection_history_id', '=', collectionHistoryId)
        .execute();
    },

    /**
     * Update a collection institute history record
     * @param {number} id - The record ID
     * @param {Object} data - The update data
     * @param {number} [data.collection_history_id] - The collection history ID
     * @param {number} [data.institute_id] - The institute ID
     * @param {number} [data.form_id] - The form ID
     * @param {number} [data.template_form_id] - The template form ID
     * @param {number} [data.collection_status_id] - The collection status ID
     * @param {number} [data.prev_collection_status_id] - The previous collection status ID
     * @param {Date} [data.execution_date] - The execution date
     * @param {boolean} [data.manual_entry] - Whether the record is a manual entry
     * @returns {Promise<Object>} The updated record
     */
    update: async (id, data) => {
      const updateFields = {};
      
      if (data.collection_history_id !== undefined) {
        updateFields.collection_history_id = data.collection_history_id;
      }
      if (data.institute_id !== undefined) {
        updateFields.institute_id = data.institute_id;
      }
      if (data.form_id !== undefined) {
        updateFields.form_id = data.form_id;
      }
      if (data.template_form_id !== undefined) {
        updateFields.template_form_id = data.template_form_id;
      }
      if (data.collection_status_id !== undefined) {
        updateFields.collection_status_id = data.collection_status_id;
      }
      if (data.prev_collection_status_id !== undefined) {
        updateFields.prev_collection_status_id = data.prev_collection_status_id;
      }
      if (data.execution_date !== undefined) {
        updateFields.execution_date = data.execution_date;
      }
      if (data.manual_entry !== undefined) {
        updateFields.manual_entry = data.manual_entry;
      }

      if (Object.keys(updateFields).length === 0) {
        return null;
      }

      return await db
        .updateTable(Tables.CollectionInstituteHistoryTable.tableName)
        .set(updateFields)
        .where('id', '=', id)
        .returning(['id', 'collection_history_id', 'institute_id', 'form_id', 'template_form_id', 'collection_status_id', 'prev_collection_status_id', 'execution_date', 'manual_entry'])
        .executeTakeFirst();
    },

    /**
     * Get paginated collection institute history records with filters
     * @param {number} offset - Page number (1-based)
     * @param {number} limit - Records per page
     * @param {Object} filters - Filter criteria
     * @param {number} [filters.form_id] - Form ID to filter by
     * @returns {Promise<Array>} Array of records
     */
    getPaginated: async (offset, limit, filters = {}) => {
      let query = db
        .selectFrom(Tables.CollectionInstituteHistoryTable.tableName + ' as cih')
        .leftJoin(
          'institutes as i',
          'cih.institute_id',
          'i.id'
        )
        .leftJoin(
          'form as f',
          'cih.form_id',
          'f.id'
        )
        .leftJoin(
          'form as tf',
          'cih.template_form_id',
          'tf.id'
        )
        .leftJoin(
          'collection_status as cs',
          'cih.collection_status_id',
          'cs.id'
        )
        .leftJoin(
          Tables.CollectionHistoryTable.tableName + ' as ch',
          'cih.collection_history_id',
          'ch.id'
        )
        .leftJoin(
          Tables.DataCollectionTable.tableName + ' as dc',
          'ch.collection_id',
          'dc.id'
        )
        .select([
          'cih.id',
          'cih.collection_history_id',
          'cih.institute_id',
          'cih.form_id',
          'cih.template_form_id',
          'cih.collection_status_id',
          'cih.prev_collection_status_id',
          'cih.execution_date',
          'i.name as institute_name',
          'i.email as institute_email',
          'i.contact_number as institute_contact',
          'f.name as form_name',
          'f.file_name as form_file_name',
          'tf.name as template_form_name',
          'tf.file_name as template_form_file_name',
          'cs.name as status_name',
          'dc.id as collection_id',
          'dc.name as collection_name',
          'dc.description as collection_description',
          'ch.open_date',
          'ch.close_date',
          'dc.collection_type_id',
          'dc.collection_window_id'
        ]);

      // Apply filters
      if (filters.collection_id) {
        if (Array.isArray(filters.collection_id)) {
          query = query.where('dc.id', 'in', filters.collection_id);
        } else {
          query = query.where('dc.id', '=', filters.collection_id);
        }
      }
      if (filters.collection_history_id) {
        if (Array.isArray(filters.collection_history_id)) {
          query = query.where('cih.collection_history_id', 'in', filters.collection_history_id);
        } else {
          query = query.where('cih.collection_history_id', '=', filters.collection_history_id);
        }
      }
      if (filters.institute_id) {
        if (Array.isArray(filters.institute_id)) {
          query = query.where('cih.institute_id', 'in', filters.institute_id);
        } else {
          query = query.where('cih.institute_id', '=', filters.institute_id);
        }
      }
      if (filters.form_id) {
        if (Array.isArray(filters.form_id)) {
          query = query.where('cih.form_id', 'in', filters.form_id);
        } else {
          query = query.where('cih.form_id', '=', filters.form_id);
        }
      }
      if (filters.collection_status_id) {
        if (Array.isArray(filters.collection_status_id)) {
          query = query.where('cih.collection_status_id', 'in', filters.collection_status_id);
        } else {
          query = query.where('cih.collection_status_id', '=', filters.collection_status_id);
        }
      }
      if (filters.year) {
        query = query.where(sql`EXTRACT(YEAR FROM cih.execution_date)`, '=', filters.year);
      }

      // Name filter for collection name
      if (filters.name) {
        query = query.where('dc.name', 'ilike', `%${filters.name}%`);
      }

      if (limit) {
        query = query.limit(limit);
      }
      if (offset) {
        query = query.offset(offset);
      }

      // Always sort by execution_date descending
      query = query.orderBy('cih.execution_date', 'desc');

      return await query.execute();
    },

    /**
     * Get total count of collection institute history records with filters
     * @param {Object} filters - Filter criteria
     * @returns {Promise<number>} Total count
     */
    getTotalCount: async (filters = {}) => {
      let query = db
        .selectFrom(Tables.CollectionInstituteHistoryTable.tableName + ' as cih')
        .leftJoin(
          'institutes as i',
          'cih.institute_id',
          'i.id'
        )
        .leftJoin(
          'form as f',
          'cih.form_id',
          'f.id'
        )
        .leftJoin(
          'form as tf',
          'cih.template_form_id',
          'tf.id'
        )
        .leftJoin(
          'collection_status as cs',
          'cih.collection_status_id',
          'cs.id'
        )
        .leftJoin(
          Tables.CollectionHistoryTable.tableName + ' as ch',
          'cih.collection_history_id',
          'ch.id'
        )
        .leftJoin(
          Tables.DataCollectionTable.tableName + ' as dc',
          'ch.collection_id',
          'dc.id'
        )
        .select(db.fn.count('cih.id').as('count'));

      // Apply filters
      if (filters.collection_id) {
        if (Array.isArray(filters.collection_id)) {
          query = query.where('dc.id', 'in', filters.collection_id);
        } else {
          query = query.where('dc.id', '=', filters.collection_id);
        }
      }
      if (filters.collection_history_id) {
        if (Array.isArray(filters.collection_history_id)) {
          query = query.where('cih.collection_history_id', 'in', filters.collection_history_id);
        } else {
          query = query.where('cih.collection_history_id', '=', filters.collection_history_id);
        }
      }
      if (filters.institute_id) {
        if (Array.isArray(filters.institute_id)) {
          query = query.where('cih.institute_id', 'in', filters.institute_id);
        } else {
          query = query.where('cih.institute_id', '=', filters.institute_id);
        }
      }
      if (filters.form_id) {
        if (Array.isArray(filters.form_id)) {
          query = query.where('cih.form_id', 'in', filters.form_id);
        } else {
          query = query.where('cih.form_id', '=', filters.form_id);
        }
      }
      if (filters.collection_status_id) {
        if (Array.isArray(filters.collection_status_id)) {
          query = query.where('cih.collection_status_id', 'in', filters.collection_status_id);
        } else {
          query = query.where('cih.collection_status_id', '=', filters.collection_status_id);
        }
      }
      if (filters.year) {
        query = query.where(sql`EXTRACT(YEAR FROM cih.execution_date)`, '=', filters.year);
      }

      // Name filter for collection name
      if (filters.name) {
        query = query.where('dc.name', 'ilike', `%${filters.name}%`);
      }

      const result = await query.executeTakeFirst();
      return parseInt(result.count, 10);
    },

    /**
     * Get institute and form information for a specific history ID and institute
     * @param {number} historyId - The collection history ID
     * @param {number} instituteId - The institute ID
     * @returns {Promise<Array>} Array of records with institute and form details
     */
    getCollectionHistoryByInstitute: async (historyId, instituteId) => {
      const results = await db
        .selectFrom(Tables.CollectionInstituteHistoryTable.tableName + ' as cih')
        .innerJoin(
          'institutes as i',
          'cih.institute_id',
          'i.id'
        )
        .leftJoin(
          'form as f',
          'cih.form_id',
          'f.id'
        )
        .leftJoin(
          'form as tf',
          'cih.template_form_id',
          'tf.id'
        )
        .innerJoin(
          'collection_status as cs',
          'cih.collection_status_id',
          'cs.id'
        )
        .innerJoin(
          Tables.CollectionHistoryTable.tableName + ' as ch',
          'cih.collection_history_id',
          'ch.id'
        )
        .innerJoin(
          Tables.DataCollectionTable.tableName + ' as dc',
          'ch.collection_id',
          'dc.id'
        )
        .select([
          'cih.id',
          'cih.collection_history_id',
          'cih.institute_id',
          'cih.form_id',
          'cih.template_form_id',
          'cih.collection_status_id',
          'cih.execution_date',
          'cih.manual_entry',
          'i.name as institute_name',
          'i.email as institute_email',
          'i.contact_number as institute_contact',
          'f.name as form_name',
          'f.file_name as form_file_name',
          'tf.name as template_form_name',
          'tf.file_name as template_form_file_name',
          'tf.manual_entry_mappings',
          'cs.name as status_name',
          'dc.id as collection_id',
          'dc.name as collection_name',
          'dc.description as collection_description',
          'ch.open_date',
          'ch.close_date',
          'dc.collection_type_id',
          'dc.collection_window_id'
        ])
        .where('cih.collection_history_id', '=', parseInt(historyId))
        .where('cih.institute_id', '=', parseInt(instituteId))
        .execute();

      // Generate presigned URLs for forms
      const resultsWithUrls = await Promise.all(
        results.map(async (result) => {
          if (result.form_file_name) {
            result.download_url = await generateFormPresignedUrl(result.form_id, result.form_file_name);
          }
          if (result.template_form_file_name) {
            result.template_download_url = await generateFormPresignedUrl(result.template_form_id, result.template_form_file_name);
          }
          return result;
        })
      );

      return resultsWithUrls;
    },

    /**
     * Get institute and form information for all institutes in a specific history
     * @param {number} historyId - The collection history ID
     * @returns {Promise<Array>} Array of records with institute and form details
     */
    getInstitutesCollectionHistory: async (historyId) => {
      return await db
        .selectFrom(Tables.CollectionInstituteHistoryTable.tableName + ' as cih')
        .innerJoin(
          'institutes as i',
          'cih.institute_id',
          'i.id'
        )
        .leftJoin(
          'form as f',
          'cih.form_id',
          'f.id'
        )
        .leftJoin(
          'form as tf',
          'cih.template_form_id',
          'tf.id'
        )
        .innerJoin(
          'collection_status as cs',
          'cih.collection_status_id',
          'cs.id'
        )
        .innerJoin(
          Tables.CollectionHistoryTable.tableName + ' as ch',
          'cih.collection_history_id',
          'ch.id'
        )
        .innerJoin(
          Tables.DataCollectionTable.tableName + ' as dc',
          'ch.collection_id',
          'dc.id'
        )
        .select([
          'cih.id',
          'cih.collection_history_id',
          'cih.institute_id',
          'cih.form_id',
          'cih.template_form_id',
          'cih.collection_status_id',
          'cih.execution_date',
          'i.name as institute_name',
          'i.email as institute_email',
          'i.contact_number as institute_contact',
          'f.name as form_name',
          'f.file_name as form_file_name',
          'tf.name as template_form_name',
          'tf.file_name as template_form_file_name',
          'cs.name as status_name',
          'dc.id as collection_id',
          'dc.name as collection_name',
          'dc.description as collection_description',
          'ch.open_date',
          'ch.close_date',
          'dc.collection_type_id',
          'dc.collection_window_id'
        ])
        .where('cih.collection_history_id', '=', historyId)
        .execute();
    }
  }
});
