AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: >
  DOAP Email Layer
  
  Lambda layer for sending emails using AWS SES in DOAP services

Resources:
  EmailLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: doap-email-layer
      Description: Email service layer for DOAP services
      ContentUri: .
      CompatibleRuntimes:
        - nodejs18.x
        - nodejs20.x
      LicenseInfo: UNLICENSED
      RetentionPolicy: Retain
    Metadata:
      BuildMethod: nodejs20.x

Outputs:
  EmailLayerArn:
    Description: ARN of the email layer
    Value: !Ref EmailLayer
    Export:
      Name: !Sub "${AWS::StackName}-EmailLayerArn"
  
  EmailLayerVersion:
    Description: Version of the email layer
    Value: !Ref EmailLayer
    Export:
      Name: !Sub "${AWS::StackName}-EmailLayerVersion" 