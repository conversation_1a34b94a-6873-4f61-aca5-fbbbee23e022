# Email Layer

This layer provides email functionality using AWS SES (Simple Email Service).

## Prerequisites

1. AWS SES must be configured in your AWS account
2. The sender email address must be verified in SES
3. If you're in the SES sandbox, recipient email addresses must also be verified

## Usage

```javascript
const { sendEmail } = require('email-layer');

// Example usage
await sendEmail({
  to: ['<EMAIL>'],
  cc: ['<EMAIL>'],
  bcc: ['<EMAIL>'],
  from: '<EMAIL>',
  subject: 'Test Email',
  message: 'This is a test email'
});
```

## Parameters

- `to` (required): Array of recipient email addresses
- `cc` (optional): Array of CC email addresses
- `bcc` (optional): Array of BCC email addresses
- `from` (required): Sender email address (must be verified in SES)
- `subject` (required): Email subject
- `message` (required): Email message body

## Error Handling

The function will throw an error if:
- Any required parameter is missing
- The sender email is not verified in SES
- The recipient emails are not verified (if in SES sandbox)
- There are any AWS SES service errors

## AWS Permissions

The Lambda function using this layer needs the following IAM permissions:
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "ses:SendEmail",
        "ses:SendRawEmail"
      ],
      "Resource": "*"
    }
  ]
}
``` 