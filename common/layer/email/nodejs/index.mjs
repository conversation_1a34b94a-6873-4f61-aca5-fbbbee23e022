import { SESClient, SendEmailCommand } from '@aws-sdk/client-ses';

const sesClient = new SESClient({ region: process.env.DEFAULT_REGION });

/**
 * Sends an email using AWS SES
 * @param {Object} params - Email parameters
 * @param {string|string[]} params.to - Recipient email address(es)
 * @param {string[]} [params.cc] - List of CC email addresses
 * @param {string[]} [params.bcc] - List of BCC email addresses
 * @param {string} params.from - Sender email address
 * @param {string} params.subject - Email subject
 * @param {string} params.message - Email message body
 * @returns {Promise<Object>} - SES response
 */
export const sendEmail = async ({ to, cc, bcc, from, subject, message }) => {
  try {
    // Ensure to is an array
    const toAddresses = Array.isArray(to) ? to : [to];

    // Log the email parameters
    console.log('Email parameters:', {
      to: toAddresses,
      cc,
      bcc,
      from,
      subject,
      message
    });

    const params = {
      Source: from,
      Destination: {
        ToAddresses: toAddresses,
        CcAddresses: cc || [],
        BccAddresses: bcc || []
      },
      Message: {
        Subject: {
          Data: subject,
          Charset: 'UTF-8'
        },
        Body: {
          Text: {
            Data: message,
            Charset: 'UTF-8'
          }
        }
      }
    };

    const command = new SendEmailCommand(params);
    const response = await sesClient.send(command);
    
    console.info('Email sent successfully:', response);
    return response;
  } catch (error) {
    console.error('Error sending email:', error);
    throw error;
  }
}; 