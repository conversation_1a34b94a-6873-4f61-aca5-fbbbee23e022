AWSTemplateFormatVersion: '2010-09-09'
Description: 'CloudFormation template with CloudFront distribution routing to Collections and Institutes APIs'

Parameters:
  ApiStage:
    Type: String
    Description: 'The API Gateway stage'
    AllowedValues:
      - prod
      - dev
      - qa
    Default: prod

  DomainName:
    Type: String
    Description: 'Base domain name (e.g., example.com)'

Resources:
  # ACM Certificate for CloudFront (must be in us-east-1)
  CloudFrontCertificate:
    Type: AWS::CertificateManager::Certificate
    Properties:
      DomainName: !Ref DomainName
      ValidationMethod: DNS
      DomainValidationOptions:
        - DomainName: !Ref DomainName
          HostedZoneId: !ImportValue "DOAP-BASE-INFRA-ROUTE53-STACK-HostedZoneId"

  # CloudFront Distribution
  ApiCloudFrontDistribution:
    Type: AWS::CloudFront::Distribution
    Properties:
      DistributionConfig:
        Aliases:
          - !Ref DomainName
        DefaultCacheBehavior:
          TargetOriginId: default-origin
          ViewerProtocolPolicy: redirect-to-https
          CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad  # CachingDisabled
          OriginRequestPolicyId: b689b0a8-53d0-40ab-baf2-68738e2966ac
        CacheBehaviors:
          - PathPattern: '/collections*'
            TargetOriginId: collections-api
            ViewerProtocolPolicy: redirect-to-https
            CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad  # CachingDisabled
            OriginRequestPolicyId: b689b0a8-53d0-40ab-baf2-68738e2966ac
            AllowedMethods:
              - GET
              - HEAD
              - OPTIONS
              - PUT
              - POST
              - PATCH
              - DELETE
            ForwardedValues:
              QueryString: true
              Cookies:
                Forward: none
          - PathPattern: '/institutes*'
            TargetOriginId: institutes-api
            ViewerProtocolPolicy: redirect-to-https
            CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad  # CachingDisabled
            OriginRequestPolicyId: b689b0a8-53d0-40ab-baf2-68738e2966ac
            AllowedMethods:
              - GET
              - HEAD
              - OPTIONS
              - PUT
              - POST
              - PATCH
              - DELETE
            ForwardedValues:
              QueryString: true
              Cookies:
                Forward: none
          - PathPattern: '/send-email*'
            TargetOriginId: send-email-api
            ViewerProtocolPolicy: redirect-to-https
            CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad  # CachingDisabled
            OriginRequestPolicyId: b689b0a8-53d0-40ab-baf2-68738e2966ac
            AllowedMethods:
              - GET
              - HEAD
              - OPTIONS
              - PUT
              - POST
              - PATCH
              - DELETE
            ForwardedValues:
              QueryString: true
              Cookies:
                Forward: none
        Origins:
          - Id: default-origin
            DomainName: 
              Fn::Join:
                - "."
                - - Fn::ImportValue: 
                      Fn::Sub: "DOAP-SERVERLESS-STACK-collections-api-${ApiStage}-ApiGatewayRestApiId-${ApiStage}"
                  - Fn::Sub: "execute-api.${AWS::Region}.amazonaws.com"
            CustomOriginConfig:
              HTTPPort: 443
              OriginProtocolPolicy: https-only
            OriginPath: !Sub '/${ApiStage}'
          - Id: collections-api
            DomainName: 
              Fn::Join:
                - "."
                - - Fn::ImportValue: 
                      Fn::Sub: "DOAP-SERVERLESS-STACK-collections-api-${ApiStage}-ApiGatewayRestApiId-${ApiStage}"
                  - Fn::Sub: "execute-api.${AWS::Region}.amazonaws.com"
            CustomOriginConfig:
              HTTPPort: 443
              OriginProtocolPolicy: https-only
            OriginPath: !Sub '/${ApiStage}'
          - Id: institutes-api
            DomainName: 
              Fn::Join:
                - "."
                - - Fn::ImportValue: 
                      Fn::Sub: "DOAP-SERVERLESS-STACK-institutions-api-${ApiStage}-ApiGatewayRestApiId-${ApiStage}"
                  - Fn::Sub: "execute-api.${AWS::Region}.amazonaws.com"
            CustomOriginConfig:
              HTTPPort: 443
              OriginProtocolPolicy: https-only
            OriginPath: !Sub '/${ApiStage}'
          - Id: send-email-api
            DomainName: 
              Fn::Join:
                - "."
                - - Fn::ImportValue: 
                      Fn::Sub: "DOAP-SERVERLESS-STACK-send-email-api-${ApiStage}-ApiGatewayRestApiId-${ApiStage}"
                  - Fn::Sub: "execute-api.${AWS::Region}.amazonaws.com"
            CustomOriginConfig:
              HTTPPort: 443
              OriginProtocolPolicy: https-only
            OriginPath: !Sub '/${ApiStage}'
        Enabled: true
        HttpVersion: http2
        PriceClass: PriceClass_100
        ViewerCertificate:
          AcmCertificateArn: !Ref CloudFrontCertificate
          SslSupportMethod: sni-only
          MinimumProtocolVersion: TLSv1.2_2021

  # Route53 Record for CloudFront
  CloudFrontRoute53Record:
    Type: AWS::Route53::RecordSet
    Properties:
      HostedZoneId: !ImportValue "DOAP-BASE-INFRA-ROUTE53-STACK-HostedZoneId"
      Name: !Ref DomainName
      Type: A
      AliasTarget:
        DNSName: !GetAtt ApiCloudFrontDistribution.DomainName
        HostedZoneId: Z2FDTNDATAQYW2  # CloudFront hosted zone ID

Outputs:
  ApiStage:
    Description: 'API Gateway Stage'
    Value: !Ref ApiStage

  CloudFrontDistributionId:
    Description: 'CloudFront Distribution ID'
    Value: !Ref ApiCloudFrontDistribution

  CloudFrontDomainName:
    Description: 'CloudFront Distribution Domain Name'
    Value: !GetAtt ApiCloudFrontDistribution.DomainName

  CertificateArn:
    Description: 'ACM Certificate ARN'
    Value: !Ref CloudFrontCertificate
