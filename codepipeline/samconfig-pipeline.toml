version = 0.1

[1]
[1.global]
[1.global.parameters]
stack_name = "DOAP-SERVERLESS-STACK-Dev"
s3_prefix = "DOAP-SERVERLESS-STACK-Dev"
s3_bucket = "doap-mono-serverless-211125316254-dev"
region = "us-east-1"

[1.deploy]
[1.deploy.parameters]
fail_on_empty_changeset = false
capabilities = "CAPABILITY_IAM"

# ############################################## #

[2]
[2.global]
[2.global.parameters]
stack_name = "DOAP-SERVERLESS-STACK-prod"
s3_prefix = "DOAP-SERVERLESS-STACK-prod"
s3_bucket = "doap-mono-serverless-211125316254-prod"
region = "us-east-1"

[2.deploy]
[2.deploy.parameters]
template = "packaged-prod.yaml"
fail_on_empty_changeset = false
capabilities = "CAPABILITY_IAM"

# ############################################## #

[pipeline]
[pipeline.deploy]
[pipeline.deploy.parameters]
stack_name = "DOAP-SERVERLESS-PIPELINE-STACK-Dev"
s3_prefix = "DOAP-SERVERLESS-PIPELINE-STACK-Dev"
resolve_s3 = true
fail_on_empty_changeset = false
template = "codepipeline.yaml"
capabilities = "CAPABILITY_IAM"
