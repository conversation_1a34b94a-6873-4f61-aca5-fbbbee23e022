version: 0.2
phases:
  install:
    runtime-versions:
      python: 3.11
    commands:
      - pip install --upgrade pip
      - pip install --upgrade awscli aws-sam-cli
      # Enable docker https://docs.aws.amazon.com/codebuild/latest/userguide/sample-docker-custom-image.html
      - nohup /usr/local/bin/dockerd --host=unix:///var/run/docker.sock --host=tcp://127.0.0.1:2375 --storage-driver=overlay2 &
      - timeout 15 sh -c "until docker info; do echo .; sleep 1; done"
  build:
    commands:
      - echo ${PROJECT_SUBFOLDERS}
      - chmod +x codepipeline/scripts/build_package.sh
      - chmod +x codepipeline/scripts/install_database_layer.sh
      - chmod +x codepipeline/scripts/install_email_layer.sh
      # - cd "${PROJECT_SUBFOLDERS}" # we may want this to run for each sub folder so the pipeline may need to do this for all subfolders that had a change.
      - . codepipeline/scripts/install_database_layer.sh
      - . codepipeline/scripts/install_email_layer.sh
      - . codepipeline/scripts/build_package.sh --sub_dirs="${PROJECT_SUBFOLDERS}"


artifacts:
  files:
    - /**/packaged-testing-*.yaml
    - /**/packaged-prod-*.yaml
    - codepipeline/*
# Moveable to parent since it just does package build in the correct container,
# with the correct SAM template.yaml which is converted to CFN template using
# the build and all dependencies are packaged together
# Artifacts might not be copied if this is supposed to tell them what files to
# copy andy not where. In that case removing the project folder prefix might be the fix

#HOWEVER SAM COMMANDS ARE RAN FOR EACH SAM APP SO WE MAY NEED TO RUN THOSE WITHIN EACH FOLDER
