version: 0.2
phases:
  install:
    runtime-versions:
      python: 3.11
    commands:
      - pip install --upgrade pip
      - pip install --upgrade awscli aws-sam-cli
  build:
    commands:
      # - cd "${PROJECT_SUBFOLDERS}"
      - . codepipeline/scripts/deploy.sh --sub_dirs="${PROJECT_SUBFOLDERS}"
      # - . codepipeline/assume-role.sh "${ENV_PIPELINE_EXECUTION_ROLE}" deploy
      # - sam deploy  --config-file codepipeline/samconfig-pipeline.toml
      #   --config-env "${ENV_CONFIG_NAME}"
      #   --role-arn "${ENV_CLOUDFORMATION_EXECUTION_ROLE}"
# Moveable to parent since it just does deploy
# 1. Move assume-role to parent folder
# 2. Move this to the parent folder with subchild called codepipeline
#HOWEVER SAM COMMANDS ARE RAN FOR EACH SAM APP SO WE MAY NEED TO RUN THOSE WITHIN EACH FOLDER