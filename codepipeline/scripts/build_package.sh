#!/bin/sh

echo "Running script with options $@"

# Accept a named argument for directories (e.g., --sub_dirs="dir1,dir2,dir3")
for i in "$@"
do
case $i in
    --sub_dirs=*)
    SUB_DIRS="${i#*=}"
    echo "Got subdirectories: $SUB_DIRS"
    shift # past argument=value
    ;;
    *)
          # unknown option
    ;;
esac
done

# Check if SUB_DIRS is empty
if [ -z "$SUB_DIRS" ]; then
    echo "No subdirectories found."
    exit 1
fi

# Convert comma-separated string to an array of directories
IFS=',' read -r -a dirs_array <<< "$SUB_DIRS"

# Handle the case where there is only one directory without commas
if [ ${#dirs_array[@]} -eq 0 ] && [ -n "$SUB_DIRS" ]; then
    dirs_array=("$SUB_DIRS")
fi

# Get the root directory (where the script is being run from)
ROOT_DIR=$(pwd)
echo "Root directory: $ROOT_DIR"

# Loop through each directory
for dir in "${dirs_array[@]}"
do
    echo "Building and Packaging for directory: $dir"
    
    # Check if directory exists
    if [ ! -d "$dir" ]; then
        echo "Error: Directory $dir does not exist"
        continue
    fi

    # Change to the directory
    cd "$dir" || {
        echo "Error: Failed to change to directory $dir"
        continue
    }

    echo "Current directory: $(pwd)"

    # Perform steps within the directory
    echo "Building SAM application..."
    sam build --use-container --template "template.yaml" || {
        echo "Error: SAM build failed for $dir"
        cd "$ROOT_DIR"
        continue
    }
    echo "Built package"

    echo "Assuming dev role..."
    . ../codepipeline/assume-role.sh "${TESTING_PIPELINE_EXECUTION_ROLE}" test-package || {
        echo "Error: Failed to assume dev role"
        cd "$ROOT_DIR"
        continue
    }
    echo "Assumed dev role"

    echo "Packaging dev environment..."
    sam package --config-file ../codepipeline/samconfig-pipeline.toml \
        --config-env "${TESTING_ENV_CONFIG_NAME}" \
        --output-template-file "packaged-testing-${dir}.yaml" || {
        echo "Error: Failed to package dev environment"
        cd "$ROOT_DIR"
        continue
    }
    echo "Packaged dev env package"

    echo "Assuming prod role..."
    . ../codepipeline/assume-role.sh "${PROD_PIPELINE_EXECUTION_ROLE}" prod-package || {
        echo "Error: Failed to assume prod role"
        cd "$ROOT_DIR"
        continue
    }
    echo "Assumed prod role"

    echo "Packaging prod environment..."
    sam package --config-file ../codepipeline/samconfig-pipeline.toml \
        --config-env "${PROD_ENV_CONFIG_NAME}" \
        --output-template-file "packaged-prod-${dir}.yaml" || {
        echo "Error: Failed to package prod environment"
        cd "$ROOT_DIR"
        continue
    }
    echo "Packaged prod env package"

    echo "Deploying development packages..."
    . ../codepipeline/assume-role.sh "${ENV_PIPELINE_EXECUTION_ROLE}" deploy || {
        echo "Error: Failed to assume deploy role"
        cd "$ROOT_DIR"
        continue
    }
    echo "Assumed deploy role"

    echo "Deploying using config environment: ${ENV_CONFIG_NAME} with API stage: ${BUILD_STAGE}"
    sam deploy --config-file ../codepipeline/samconfig-pipeline.toml \
        --template "packaged-testing-${dir}.yaml" \
        --config-env "${ENV_CONFIG_NAME}" \
        --role-arn "${ENV_CLOUDFORMATION_EXECUTION_ROLE}" \
        --stack-name "DOAP-SERVERLESS-STACK-${dir}-${BUILD_STAGE}" \
        --parameter-overrides "Environment=${ENV_CONFIG_NAME} ApiStage=${BUILD_STAGE}" || {
        echo "Error: Failed to deploy $dir"
        cd "$ROOT_DIR"
        continue
    }

    echo "Successfully deployed $dir"
    
    # Go back to the root directory
    cd "$ROOT_DIR" || {
        echo "Error: Failed to return to root directory"
        exit 1
    }
done