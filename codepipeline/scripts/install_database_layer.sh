#!/bin/sh

echo "Installing database layer dependencies..."
echo "Running script with options $@"

# Navigate to the database layer directory
cd common/layer/database

# Check if package.json exists
if [ ! -f "package.json" ]; then
    echo "Error: package.json not found in common/layer/database"
    exit 1
fi

# Install dependencies
echo "Running npm install..."
npm install

# Check if installation was successful
if [ $? -eq 0 ]; then
    echo "Database layer dependencies installed successfully"
else
    echo "Error: npm install failed"
    exit 1
fi

# Go back to the original directory
cd ../../.. 