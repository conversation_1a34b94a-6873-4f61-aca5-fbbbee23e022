#!/bin/sh

# Accept a named argument for directories (e.g., --sub_dirs="dir1,dir2,dir3")
for i in "$@"
do
case $i in
    --dirs=*)
    SUB_DIRS="${i#*=}"
    shift # past argument=value
    ;;
    *)
          # unknown option
    ;;
esac
done

# Convert comma-separated string to an array of directories
IFS=',' read -r -a dirs_array <<< "$SUB_DIRS"

# Loop through each directory
for dir in "${dirs_array[@]}"
do
    echo "Deploying directory: $dir"
    cd "$dir"

    # Perform steps within the directory

    # . ../codepipeline/assume-role.sh "${ENV_PIPELINE_EXECUTION_ROLE}" deploy
    # sam deploy  --config-file ../codepipeline/samconfig-pipeline.toml
    #     --config-env "${ENV_CONFIG_NAME}"
    #     --role-arn "${ENV_CLOUDFORMATION_EXECUTION_ROLE}"
    
    # Go back to the parent directory
    cd ..
done