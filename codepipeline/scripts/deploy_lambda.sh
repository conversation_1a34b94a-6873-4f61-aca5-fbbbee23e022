#!/bin/sh
cd codepipeline-lambda
zip -r ../codepipeline-lambda.zip .
cd ../
aws s3 cp codepipeline-lambda.zip "s3://doap-mono-serverless-pipeline-lambda-${ACCOUNT_ID}-${DEPLOYMENT_STAGE}"
echo "Checking Lambda Function Existence"
aws --version
AWS_PAGER="" aws lambda get-function --function-name "serverless-pipeline-lambda-${ACCOUNT_ID}-${DEPLOYMENT_STAGE}" > /dev/null 2>&1
if test $? -eq 0; then
    echo "Lambda exists"
    aws lambda update-function-code --function-name "serverless-pipeline-lambda-${ACCOUNT_ID}-${DEPLOYMENT_STAGE}" --s3-bucket "doap-mono-serverless-pipeline-lambda-${ACCOUNT_ID}-${DEPLOYMENT_STAGE}" --s3-key codepipeline-lambda.zip
fi

