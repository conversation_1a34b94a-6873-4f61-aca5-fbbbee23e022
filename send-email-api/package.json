{"name": "send-email-api", "version": "1.0.0", "description": "DOAP Send Email API", "main": "src/handlers/send-email.mjs", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "sam build", "deploy": "sam <PERSON>"}, "dependencies": {"@aws-sdk/client-ses": "^3.0.0", "@aws-sdk/client-ssm": "^3.0.0", "joi": "^17.12.0"}, "devDependencies": {"@aws-sdk/types": "^3.0.0", "@types/node": "^20.0.0", "aws-sam-cli-local": "^0.1.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.0"}, "engines": {"node": ">=18.0.0"}, "author": "NCICU", "license": "ISC"}