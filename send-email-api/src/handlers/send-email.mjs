import { sendEmail } from '/opt/nodejs/index.mjs';
import { createResponse } from '../utils/response-utils.mjs';
import { STATUSCODE } from '../constants/status-codes.mjs';

/**
 * Handler for POST /send-email endpoint
 * Sends an email using AWS SES
 */
export const sendEmailHandler = async (event) => {
  try {
    // Parse request body
    const body = JSON.parse(event.body);
    const { to, cc, bcc, from, subject, message } = body;

    // Validate required fields
    if (!to || !subject || !message) {
      return createResponse(STATUSCODE.BAD_REQUEST, {
        message: "Missing required fields. Required: to, subject, message"
      });
    }

    // Use default from email if not provided
    const fromEmail = from || process.env.DEFAULT_FROM_EMAIL;

    // Send email
    const result = await sendEmail({
      to: Array.isArray(to) ? to : [to],
      cc: cc ? (Array.isArray(cc) ? cc : [cc]) : undefined,
      bcc: bcc ? (Array.isArray(bcc) ? bcc : [bcc]) : undefined,
      from: fromEmail,
      subject,
      message
    });

    // Create successful response
    const response = createResponse(
      STATUSCODE.SUCCESS,
      {
        message: "Email sent successfully",
        messageId: result.MessageId
      }
    );

    // Log response details
    console.info(
      `Response from: ${event.path} statusCode: ${response.statusCode} messageId: ${result.MessageId}`
    );

    return response;
  } catch (err) {
    // Log the error
    console.error("Error sending email:", err);

    // Return appropriate error response
    return createResponse(
      err.statusCode || STATUSCODE.SERVER_ERROR,
      { message: "Error sending email", error: err.toString() }
    );
  }
}; 