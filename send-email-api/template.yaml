AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: >
  Send Email API using AWS SES
  
  This API provides endpoints for sending emails using AWS SES.

Parameters:
  Stage:
    Type: String
    Default: dev
    AllowedValues:
      - prod
      - dev
      - qa
    Description: Deployment stage (prod/dev/qa)

  DefaultFromEmail:
    Type: String
    Default: <EMAIL>
    Description: Default sender email address

Resources:
  # Email layer definition
  EmailLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: !Sub "doap-email-layer-${Stage}"
      Description: Email service using AWS SES
      ContentUri: ../common/layer/email
      CompatibleRuntimes:
        - nodejs20.x
      RetentionPolicy: Retain

  # API Gateway definition
  ApiGateway:
    Type: AWS::Serverless::Api
    Properties:
      StageName: !Ref Stage
      Cors:
        AllowMethods: "'GET,POST,OPTIONS'"
        AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
        AllowOrigin: "'*'"

  SendEmailFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: src/
      Handler: handlers/send-email.sendEmailHandler
      Runtime: nodejs20.x
      Environment:
        Variables:
          DEFAULT_REGION: !Ref AWS::Region
          DEFAULT_FROM_EMAIL: !Ref DefaultFromEmail
      MemorySize: 128
      Timeout: 30
      Layers:
        - !Ref EmailLayer
      Events:
        Api:
          Type: Api
          Properties:
            Path: /send-email
            Method: POST
            RestApiId: !Ref ApiGateway
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - ses:SendEmail
                - ses:SendRawEmail
              Resource: '*'

  OptionsFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: src/
      Handler: handlers/options-handler.optionsHandler
      Runtime: nodejs20.x
      MemorySize: 128
      Timeout: 30
      Description: Handles OPTIONS requests for CORS preflight
      Layers:
        - !Ref EmailLayer
      Events:
        Api:
          Type: Api
          Properties:
            Path: /send-email
            Method: OPTIONS
            RestApiId: !Ref ApiGateway

Outputs:
  ApiGatewayRestApiId:
    Description: The ID of the API Gateway created by SAM
    Value: !Ref ApiGateway
    Export:
      Name: !Sub "${AWS::StackName}-ApiGatewayRestApiId-${Stage}"

  SendEmailApi:
    Description: "API Gateway endpoint URL for Send Email function"
    Value: !Sub "https://${ApiGateway}.execute-api.${AWS::Region}.amazonaws.com/${Stage}/send-email"

  SendEmailFunction:
    Description: "Send Email Lambda Function ARN"
    Value: !GetAtt SendEmailFunction.Arn
    Export:
      Name: !Sub "${AWS::StackName}-SendEmailFunction"
  
  SendEmailFunctionRole:
    Description: "Implicit IAM Role created for Send Email function"
    Value: !GetAtt SendEmailFunctionRole.Arn
    Export:
      Name: !Sub "${AWS::StackName}-SendEmailFunctionRole" 