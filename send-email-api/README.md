# Send Email API

This API provides an endpoint for sending emails using AWS SES.

## API Endpoint

### POST /send-email

Sends an email using AWS SES.

#### Request Body

```json
{
  "to": ["<EMAIL>"],  // Required, can be string or array
  "cc": ["<EMAIL>"],        // Optional, can be string or array
  "bcc": ["<EMAIL>"],      // Optional, can be string or array
  "from": "<EMAIL>",    // Required
  "subject": "Test Email",         // Required
  "message": "This is a test email" // Required
}
```

#### Response

Success (200):
```json
{
  "message": "Email sent successfully",
  "messageId": "0102018c7e9f9b00-1234567890abcdef-000000"
}
```

Error (400):
```json
{
  "message": "Missing required fields. Required: to, from, subject, message"
}
```

Error (500):
```json
{
  "message": "Error sending email",
  "error": "Error message details"
}
```

## Development

### Prerequisites
- Node.js 20.x
- AWS SAM CLI
- Docker (for local testing)

### Local Development
1. Install dependencies:
```bash
cd common/layer/email
npm install

cd ../../send-email-api
npm install
```

2. Start local API:
```bash
sam local start-api
```

3. Test the endpoint:
```bash
curl -X POST http://localhost:3000/send-email \
  -H "Content-Type: application/json" \
  -d '{
    "to": ["<EMAIL>"],
    "from": "<EMAIL>",
    "subject": "Test Email",
    "message": "This is a test email"
  }'
```

### Deployment
```bash
sam build
sam deploy --guided
```

## AWS Permissions

The Lambda function requires the following IAM permissions:
- `ses:SendEmail`
- `ses:SendRawEmail`

These permissions are automatically added through the SAM template. 